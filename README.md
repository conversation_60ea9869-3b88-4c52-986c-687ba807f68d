![Veloera](https://socialify.git.ci/Veloera/Veloera/image?font=Raleway&forks=1&issues=1&name=1&owner=1&pattern=Plus&pulls=1&stargazers=1&theme=Auto)

# Veloera

[![Codacy Badge](https://app.codacy.com/project/badge/Grade/717a063ac5b8435cb78c8c3e71e6c5f3)](https://app.codacy.com/gh/Veloera/Veloera/dashboard?utm_source=gh&utm_medium=referral&utm_content=&utm_campaign=Badge_grade) [![Go Report Card](https://goreportcard.com/badge/github.com/Veloera/Veloera)](https://goreportcard.com/report/github.com/Veloera/Veloera)
[![License](https://img.shields.io/github/license/Veloera/Veloera)](https://github.com/Veloera/Veloera/blob/main/LICENSE) [![GitHub release (latest SemVer)](https://img.shields.io/github/v/release/Veloera/Veloera)](https://github.com/Veloera/Veloera/releases)
  
[![GitHub stars](https://img.shields.io/github/stars/Veloera/Veloera?style=social)](https://github.com/Veloera/Veloera/stargazers) [![Build Status](https://github.com/Veloera/Veloera/actions/workflows/docker.yml/badge.svg)](https://github.com/Veloera/Veloera/actions/workflows/docker.yml) [![Build Status](https://github.com/Veloera/Veloera/actions/workflows/release.yml/badge.svg)](https://github.com/Veloera/Veloera/actions/workflows/release.yml)  

<a href="https://opencollective.com/veloeraorg/donate" target="_blank">
  <img src="https://opencollective.com/veloeraorg/donate/button.png?color=blue" width=200 />
</a>


优秀的 AI API 网关系统

原汁原味的 New API 体验, 对界面无大改动, 遵循 GPL 3.0 协议, 无商用限制, 承诺不变质.  
添加极多原版不计划添加的特性. 以下只是部分.  

> [!IMPORTANT]  
> 我们近期更新了许可证, 查看整个 README 以了解详情

## 特性

- 支持以 `,` 分割的单渠道多 Key, 随机选取.
- 支持礼品码, 全局每用户一次, 可控制总使用次数
- 原生支持 /hf/v1 接口
- 支持正则表达式屏蔽词
- 渠道 Key 不再加密, 发送到前端显示
- 日志支持刷新
- 日志显示渠道名
- 更新加载样式
- 当没有聊天链接可用时, 不显示聊天按钮
- 空回复不计费
- 在日志表增加总/输入/输出 Tokens
- 还有更多...

## 迁移

本程序基于 new-api 二开, 数据库结构基本兼容, 会自动运行迁移.  
其他类似程序不保证支持, 后续有计划做手动迁移指南.  

### new-api

除了使用 SQLite, 均可无缝迁移.  
对于 SQLite, 建议将 `one-api.db` 重命名为 `veloera.db`, 系统会尝试自动处理, 但未经过测试. 

## 部署

> [!TIP]
> 最新版 Docker 镜像：`ghcr.io/veloera/veloera:latest`

### docker-compose

1. 克隆此仓库

```shell
git clone https://github.com/veloera/veloera.git
cd veloera
```

2. 修改配置文件

```shell
nano docker-compose.yml
```

3. 启动服务

```shell
docker-compose up -d
```

## 环境变量

- `GENERATE_DEFAULT_TOKEN`：是否为新注册用户生成初始令牌，默认为 `false`
- `STREAMING_TIMEOUT`：流式回复超时时间，默认 60 秒
- `DIFY_DEBUG`：Dify 渠道是否输出工作流和节点信息，默认 `true`
- `FORCE_STREAM_OPTION`：是否覆盖客户端 stream_options 参数，默认 `true`
- `GET_MEDIA_TOKEN`：是否统计图片 token，默认 `true`
- `GET_MEDIA_TOKEN_NOT_STREAM`：非流情况下是否统计图片 token，默认 `true`
- `UPDATE_TASK`：是否更新异步任务（Midjourney、Suno），默认 `true`
- `COHERE_SAFETY_SETTING`：Cohere 模型安全设置，可选值为 `NONE`, `CONTEXTUAL`, `STRICT`，默认 `NONE`
- `GEMINI_VISION_MAX_IMAGE_NUM`：Gemini 模型最大图片数量，默认 `16`
- `MAX_FILE_DOWNLOAD_MB`: 最大文件下载大小，单位 MB，默认 `20`
- `CRYPTO_SECRET`：加密密钥，用于加密数据库内容
- `AZURE_DEFAULT_API_VERSION`：Azure 渠道默认 API 版本，默认 `2024-12-01-preview`
- `NOTIFICATION_LIMIT_DURATION_MINUTE`：通知限制持续时间，默认 `10`分钟
- `NOTIFY_LIMIT_COUNT`：用户通知在指定持续时间内的最大数量，默认 `2`

## 赞助商

感谢这些厂商对 Veloera 的支持:  

<a href="https://edgeone.ai/?from=github" target="_blank">
  <img src="https://edgeone.ai/_next/static/media/headLogo.daeb48ad.png?auto=format&fit=max&w=200" width=200 />
</a>
<br />CDN acceleration and security protection for this project are sponsored by Tencent EdgeOne.

<br />

成为赞助者:  
<a href="https://opencollective.com/veloeraorg/donate" target="_blank">
  <img src="https://opencollective.com/veloeraorg/donate/button.png?color=blue" width=200 />
</a>

## ⚠️ 法律声明（Legal Notice）

> [!TIP]
> **TL;DR**  
> - 如果你是普通用户:  
>   此更新没有任何影响. 我们会为您处理好合规.  
> - 如果你在运行二开版本:  
>   这是一个重要更新, 如果您希望合规运行 `v0.2.27.1` 以上版本(不含), 则请仔细阅读以下更新. 
>  
> **更新概要**  
> 若您以任何形式使用、修改或分发本项目，**除遵循 GPL v3 外，还需遵守以下补充条款**：  
>  
> 1. **不得移除或遮盖**所有页面页脚或“关于”页面中的 `Powered by Veloera` 标识。  
> 2. **必须保留**项目根目录下的 `VELOERA_PROJ` 文件，且**不得修改内容**。  
> 3. **不得更改或移除** `/veloera` 路由及其功能行为。
> 
> > 本部分仅用于信息说明，不构成法律意见。如您对许可条款存在疑问或面临合规要求，强烈建议咨询法律专业人士。

自 `v0.3.27.2` 起，本项目更改为 **GPL v3 许可证**，并附加了额外使用条款。详见本文档下方“许可证”部分。  

本项目基于 `new-api` 项目，原始许可证 *Apache License 2.0* 已保留于 `new-api-stuffs/LICENSE.new-api`，截至 fork 时原项目未包含 NOTICE 文件，故无需保留。

截至 commit `c956fd3`（含该提交），项目仍遵循 **Apache 2.0 许可证**，附加条款**不适用**。  

本声明仅用于信息说明，**不构成法律建议**。如有法律合规方面疑问，请咨询专业律师。  

## 许可证

本项目自版本 `v0.3.27.2` 起，采用 **GNU 通用公共许可证第 3 版（GPL v3）** 授权，并附加以下补充条款：

### 附加条款（Additional Terms）：

除非事先获得书面授权，您在使用、修改、分发本项目时，**必须同时遵守以下附加要求**：

1. **不得移除或遮盖**所有页面页脚或“关于”页面中的 `Powered by Veloera` 徽标或文字标识。
2. **必须保留**项目根目录下的 `VELOERA_PROJ` 文件，且不得修改其内容。
3. **必须保留**并不得修改 `/veloera` 路由路径及其对应的页面行为。

这些附加条款依照 GPL v3 第 7 节的规定添加，并构成本项目许可证的组成部分。

截至 commit `c956fd3`（含），本项目代码仍遵循 Apache License 2.0，附加条款不适用。详情请参阅历史版本与 [LICENSE 文件](./LICENSE)。

> ⚠️ 本声明不构成法律建议。如您对许可证条款有任何疑问，请咨询专业法律顾问。

## 🌟 Star History

[![Star History Chart](https://api.star-history.com/svg?repos=Veloera/Veloera&type=Date)](https://star-history.com/#Veloera/Veloera&Date)

<!--

<p align="right">
   <strong>中文</strong> | <a href="./README.en.md">English</a>
</p>
<div align="center">

![new-api](/web/public/logo.png)

# New API

🍥新一代大模型网关与AI资产管理系统

<a href="https://trendshift.io/repositories/8227" target="_blank"><img src="https://trendshift.io/api/badge/repositories/8227" alt="Calcium-Ion%2Fnew-api | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>

<p align="center">
  <a href="https://raw.githubusercontent.com/Calcium-Ion/new-api/main/LICENSE">
    <img src="https://img.shields.io/github/license/Calcium-Ion/new-api?color=brightgreen" alt="license">
  </a>
  <a href="https://github.com/Calcium-Ion/new-api/releases/latest">
    <img src="https://img.shields.io/github/v/release/Calcium-Ion/new-api?color=brightgreen&include_prereleases" alt="release">
  </a>
  <a href="https://github.com/users/Calcium-Ion/packages/container/package/new-api">
    <img src="https://img.shields.io/badge/docker-ghcr.io-blue" alt="docker">
  </a>
  <a href="https://hub.docker.com/r/CalciumIon/new-api">
    <img src="https://img.shields.io/badge/docker-dockerHub-blue" alt="docker">
  </a>
  <a href="https://goreportcard.com/report/github.com/Calcium-Ion/new-api">
    <img src="https://goreportcard.com/badge/github.com/Calcium-Ion/new-api" alt="GoReportCard">
  </a>
</p>
</div>

## 📝 项目说明

> [!NOTE]
> 本项目为开源项目，在[One API](https://github.com/songquanpeng/veloera)的基础上进行二次开发

> [!IMPORTANT]
> - 本项目仅供个人学习使用，不保证稳定性，且不提供任何技术支持。
> - 使用者必须在遵循 OpenAI 的[使用条款](https://openai.com/policies/terms-of-use)以及**法律法规**的情况下使用，不得用于非法用途。
> - 根据[《生成式人工智能服务管理暂行办法》](http://www.cac.gov.cn/2023-07/13/c_1690898327029107.htm)的要求，请勿对中国地区公众提供一切未经备案的生成式人工智能服务。

## 📚 文档

详细文档请访问我们的官方Wiki：[https://docs.newapi.pro/](https://docs.newapi.pro/)

## ✨ 主要特性

New API提供了丰富的功能，详细特性请参考[特性说明](https://docs.newapi.pro/wiki/features-introduction)：

1. 🎨 全新的UI界面
2. 🌍 多语言支持
3. 💰 支持在线充值功能（易支付）
4. 🔍 支持用key查询使用额度（配合[neko-api-key-tool](https://github.com/Calcium-Ion/neko-api-key-tool)）
5. 🔄 兼容原版One API的数据库
6. 💵 支持模型按次数收费
7. ⚖️ 支持渠道加权随机
8. 📈 数据看板（控制台）
9. 🔒 令牌分组、模型限制
10. 🤖 支持更多授权登陆方式（LinuxDO,Telegram、OIDC）
11. 🔄 支持Rerank模型（Cohere和Jina），[接口文档](https://docs.newapi.pro/api/jinaai-rerank)
12. ⚡ 支持OpenAI Realtime API（包括Azure渠道），[接口文档](https://docs.newapi.pro/api/openai-realtime)
13. ⚡ 支持Claude Messages 格式，[接口文档](https://docs.newapi.pro/api/anthropic-chat)
14. 支持使用路由/chat2link进入聊天界面
15. 🧠 支持通过模型名称后缀设置 reasoning effort：
    1. OpenAI o系列模型
        - 添加后缀 `-high` 设置为 high reasoning effort (例如: `o3-mini-high`)
        - 添加后缀 `-medium` 设置为 medium reasoning effort (例如: `o3-mini-medium`)
        - 添加后缀 `-low` 设置为 low reasoning effort (例如: `o3-mini-low`)
    2. Claude 思考模型
        - 添加后缀 `-thinking` 启用思考模式 (例如: `claude-3-7-sonnet-20250219-thinking`)
16. 🔄 思考转内容功能
17. 🔄 针对用户的模型限流功能
18. 💰 缓存计费支持，开启后可以在缓存命中时按照设定的比例计费：
    1. 在 `系统设置-运营设置` 中设置 `提示缓存倍率` 选项
    2. 在渠道中设置 `提示缓存倍率`，范围 0-1，例如设置为 0.5 表示缓存命中时按照 50% 计费
    3. 支持的渠道：
        - [x] OpenAI
        - [x] Azure
        - [x] DeepSeek
        - [x] Claude

## 模型支持

此版本支持多种模型，详情请参考[接口文档-中继接口](https://docs.newapi.pro/api)：

1. 第三方模型 **gpts** （gpt-4-gizmo-*）
2. 第三方渠道[Midjourney-Proxy(Plus)](https://github.com/novicezk/midjourney-proxy)接口，[接口文档](https://docs.newapi.pro/api/midjourney-proxy-image)
3. 第三方渠道[Suno API](https://github.com/Suno-API/Suno-API)接口，[接口文档](https://docs.newapi.pro/api/suno-music)
4. 自定义渠道，支持填入完整调用地址
5. Rerank模型（[Cohere](https://cohere.ai/)和[Jina](https://jina.ai/)），[接口文档](https://docs.newapi.pro/api/jinaai-rerank)
6. Claude Messages 格式，[接口文档](https://docs.newapi.pro/api/anthropic-chat)
7. Dify，当前仅支持chatflow

## 环境变量配置

详细配置说明请参考[安装指南-环境变量配置](https://docs.newapi.pro/installation/environment-variables)：

- `GENERATE_DEFAULT_TOKEN`：是否为新注册用户生成初始令牌，默认为 `false`
- `STREAMING_TIMEOUT`：流式回复超时时间，默认60秒
- `DIFY_DEBUG`：Dify渠道是否输出工作流和节点信息，默认 `true`
- `FORCE_STREAM_OPTION`：是否覆盖客户端stream_options参数，默认 `true`
- `GET_MEDIA_TOKEN`：是否统计图片token，默认 `true`
- `GET_MEDIA_TOKEN_NOT_STREAM`：非流情况下是否统计图片token，默认 `true`
- `UPDATE_TASK`：是否更新异步任务（Midjourney、Suno），默认 `true`
- `COHERE_SAFETY_SETTING`：Cohere模型安全设置，可选值为 `NONE`, `CONTEXTUAL`, `STRICT`，默认 `NONE`
- `GEMINI_VISION_MAX_IMAGE_NUM`：Gemini模型最大图片数量，默认 `16`
- `MAX_FILE_DOWNLOAD_MB`: 最大文件下载大小，单位MB，默认 `20`
- `CRYPTO_SECRET`：加密密钥，用于加密数据库内容
- `AZURE_DEFAULT_API_VERSION`：Azure渠道默认API版本，默认 `2024-12-01-preview`
- `NOTIFICATION_LIMIT_DURATION_MINUTE`：通知限制持续时间，默认 `10`分钟
- `NOTIFY_LIMIT_COUNT`：用户通知在指定持续时间内的最大数量，默认 `2`

## 部署

详细部署指南请参考[安装指南-部署方式](https://docs.newapi.pro/installation)：

> [!TIP]
> 最新版Docker镜像：`calciumion/new-api:latest`

### 多机部署注意事项
- 必须设置环境变量 `SESSION_SECRET`，否则会导致多机部署时登录状态不一致
- 如果公用Redis，必须设置 `CRYPTO_SECRET`，否则会导致多机部署时Redis内容无法获取

### 部署要求
- 本地数据库（默认）：SQLite（Docker部署必须挂载`/data`目录）
- 远程数据库：MySQL版本 >= 5.7.8，PgSQL版本 >= 9.6

### 部署方式

#### 使用宝塔面板Docker功能部署
安装宝塔面板（**9.2.0版本**及以上），在应用商店中找到**New-API**安装即可。
[图文教程](BT.md)

#### 使用Docker Compose部署（推荐）
```shell
# 下载项目
git clone https://github.com/Calcium-Ion/new-api.git
cd new-api
# 按需编辑docker-compose.yml
# 启动
docker-compose up -d
```

#### 直接使用Docker镜像
```shell
# 使用SQLite
docker run --name new-api -d --restart always -p 3000:3000 -e TZ=Asia/Shanghai -v /home/<USER>/data/new-api:/data calciumion/new-api:latest

# 使用MySQL
docker run --name new-api -d --restart always -p 3000:3000 -e SQL_DSN="root:123456@tcp(localhost:3306)/oneapi" -e TZ=Asia/Shanghai -v /home/<USER>/data/new-api:/data calciumion/new-api:latest
```

## 渠道重试与缓存
渠道重试功能已经实现，可以在`设置->运营设置->通用设置`设置重试次数，**建议开启缓存**功能。

### 缓存设置方法
1. `REDIS_CONN_STRING`：设置Redis作为缓存
2. `MEMORY_CACHE_ENABLED`：启用内存缓存（设置了Redis则无需手动设置）

## 接口文档

详细接口文档请参考[接口文档](https://docs.newapi.pro/api)：

- [聊天接口（Chat）](https://docs.newapi.pro/api/openai-chat)
- [图像接口（Image）](https://docs.newapi.pro/api/openai-image)
- [重排序接口（Rerank）](https://docs.newapi.pro/api/jinaai-rerank)
- [实时对话接口（Realtime）](https://docs.newapi.pro/api/openai-realtime)
- [Claude聊天接口（messages）](https://docs.newapi.pro/api/anthropic-chat)

## 相关项目
- [One API](https://github.com/songquanpeng/veloera)：原版项目
- [Midjourney-Proxy](https://github.com/novicezk/midjourney-proxy)：Midjourney接口支持
- [chatnio](https://github.com/Deeptrain-Community/chatnio)：下一代AI一站式B/C端解决方案
- [neko-api-key-tool](https://github.com/Calcium-Ion/neko-api-key-tool)：用key查询使用额度

其他基于New API的项目：
- [new-api-horizon](https://github.com/Calcium-Ion/new-api-horizon)：New API高性能优化版
- [VoAPI](https://github.com/VoAPI/VoAPI)：基于New API的前端美化版本

## 帮助支持

如有问题，请参考[帮助支持](https://docs.newapi.pro/support)：
- [社区交流](https://docs.newapi.pro/support/community-interaction)
- [反馈问题](https://docs.newapi.pro/support/feedback-issues)
- [常见问题](https://docs.newapi.pro/support/faq)

## 🌟 Star History

[![Star History Chart](https://api.star-history.com/svg?repos=Calcium-Ion/new-api&type=Date)](https://star-history.com/#Calcium-Ion/new-api&Date)
-->
