// Copyright (c) 2025 Tethys Plex
//
// This file is part of Veloera.
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.
package constant

var (
	ForceFormat                     = "force_format"        // ForceFormat 强制格式化为OpenAI格式
	ChanelSettingProxy              = "proxy"               // Proxy 代理
	ChannelSettingThinkingToContent = "thinking_to_content" // ThinkingToContent
	ChannelSettingStreamSupport     = "stream_support"      // StreamSupport 控制上游流式请求行为
	StreamSupportNonStreamOnly      = "NON_STREAM_ONLY"     // StreamSupport 仅非流式请求
)
