@import './_palette.scss';

body, body[theme-mode="dark"] .semi-always-light {
--semi-color-bg-0: var(--semi-color-white);
--semi-color-bg-1: var(--semi-color-white);
--semi-color-bg-2: var(--semi-color-white);
--semi-color-bg-3: var(--semi-color-white);
--semi-color-bg-4: var(--semi-color-white);
--semi-color-info: rgba(var(--semi-blue-5), 1);
--semi-color-link: rgb(var(--semi-blue-5));
--semi-color-nav-bg: var(--semi-color-bg-1);
--semi-color-black: rgba(var(--semi-black),1);
--semi-color-data-0: rgba(87, 105, 255, 1);
--semi-color-data-1: rgba(142, 212, 231, 1);
--semi-color-data-2: rgba(245, 135, 0, 1);
--semi-color-data-3: rgba(220, 183, 252, 1);
--semi-color-data-4: rgba(74, 156, 247, 1);
--semi-color-data-5: rgba(243, 204, 53, 1);
--semi-color-data-6: rgba(254, 128, 144, 1);
--semi-color-data-7: rgba(139, 215, 210, 1);
--semi-color-data-8: rgba(131, 176, 35, 1);
--semi-color-data-9: rgba(233, 165, 229, 1);
--semi-color-fill-0: rgba(var(--semi-neutral-8),0.05);
--semi-color-fill-1: rgba(var(--semi-neutral-8),0.09);
--semi-color-fill-2: rgba(var(--semi-neutral-8),0.13);
--semi-color-text-0: rgba(var(--semi-neutral-9),1);
--semi-color-text-1: rgba(var(--semi-neutral-9),0.8);
--semi-color-text-2: rgba(var(--semi-neutral-9),0.6);
--semi-color-text-3: rgba(var(--semi-neutral-9),0.35);
--semi-color-white: rgba(var(--semi-white),1);
--semi-color-data-10: rgba(48, 167, 206, 1);
--semi-color-data-11: rgba(249, 192, 100, 1);
--semi-color-data-12: rgba(177, 113, 249, 1);
--semi-color-data-13: rgba(119, 182, 249, 1);
--semi-color-data-14: rgba(200, 143, 2, 1);
--semi-color-data-15: rgba(255, 170, 178, 1);
--semi-color-data-16: rgba(51, 176, 171, 1);
--semi-color-data-17: rgba(182, 215, 129, 1);
--semi-color-data-18: rgba(212, 88, 212, 1);
--semi-color-data-19: rgba(188, 198, 255, 1);
--semi-color-overlay-bg: rgba(22, 22, 26, 0.6);
--semi-color-border: rgba(var(--semi-neutral-1),1);
--semi-color-danger: rgba(var(--semi-red-5),1);
--semi-color-shadow: rgba(0,0,0,0.04);
--semi-color-border-1: rgba(var(--semi-neutral-2),1);
--semi-color-border-2: rgba(var(--semi-neutral-3),1);
--semi-color-border-3: rgba(var(--semi-neutral-5),1);
--semi-color-default: rgba(var(--semi-neutral-0),1);
--semi-color-info-hover: rgba(var(--semi-blue-6), 1);
--semi-color-link-hover: rgb(var(--semi-blue-4));
--semi-color-primary: rgba(var(--semi-name0-5), 1);
--semi-color-success: rgba(var(--semi-green-5),1);
--semi-color-warning: rgba(var(--semi-orange-5),1);
--semi-color-info-active: rgba(var(--semi-blue-7), 1);
--semi-color-link-active: rgb(var(--semi-blue-6));
--semi-color-link-visited: rgb(var(--semi-blue-3));
--semi-color-tertiary: rgba(var(--semi-neutral-5),1);
--semi-color-focus-border: rgb(var(--semi-blue-5));
--semi-color-info-disabled: rgba(var(--semi-blue-2), 1);
--semi-color-danger-hover: rgba(var(--semi-red-6),1);
--semi-color-highlight: rgba(var(--semi-black), 1);
--semi-color-secondary: rgba(var(--semi-yellow-5),1);
--semi-color-danger-active: rgba(var(--semi-red-7),1);
--semi-color-disabled-bg: rgb(var(--semi-neutral-4));
--semi-color-default-hover: rgba(var(--semi-neutral-1),1);
--semi-color-primary-hover: rgba(var(--semi-name0-4), 1);
--semi-color-success-hover: rgba(var(--semi-green-6),1);
--semi-color-warning-hover: rgba(var(--semi-orange-6),1);
--semi-color-default-active: rgba(var(--semi-neutral-2),1);
--semi-color-disabled-fill: rgb(var(--semi-neutral-1));
--semi-color-disabled-text: rgba(var(--semi-neutral-9),0.35);
--semi-color-highlight-bg: rgba(var(--semi-yellow-4), 1);
--semi-color-primary-active: rgba(var(--semi-name0-6), 1);
--semi-color-success-active: rgba(var(--semi-green-7),1);
--semi-color-warning-active: rgba(var(--semi-orange-7),1);
--semi-color-tertiary-hover: rgba(var(--semi-neutral-6),1);
--semi-color-disabled-border: rgb(var(--semi-neutral-3));
--semi-color-primary-disabled: rgba(var(--semi-name0-2), 1);
--semi-color-success-disabled: rgba(var(--semi-green-2),1);
--semi-color-tertiary-active: rgba(var(--semi-neutral-7),1);
--semi-color-secondary-hover: rgba(var(--semi-yellow-6),1);
--semi-color-secondary-active: rgba(var(--semi-yellow-7),1);
--semi-color-info-light-hover: rgba(var(--semi-blue-1), 1);
--semi-color-info-light-active: rgba(var(--semi-blue-2), 1);
--semi-color-secondary-disabled: rgba(var(--semi-yellow-2),1);
--semi-color-info-light-default: rgba(var(--semi-blue-0), 1);
--semi-color-danger-light-hover: rgba(var(--semi-red-1),1);
--semi-color-danger-light-active: rgba(var(--semi-red-2),1);
--semi-color-danger-light-default: rgba(var(--semi-red-0),1);
--semi-color-primary-light-hover: rgba(var(--semi-blue-1),1);
--semi-color-success-light-hover: rgba(var(--semi-green-1),1);
--semi-color-warning-light-hover: rgba(var(--semi-orange-1),1);
--semi-color-primary-light-active: rgba(var(--semi-blue-2),1);
--semi-color-success-light-active: rgba(var(--semi-green-2),1);
--semi-color-warning-light-active: rgba(var(--semi-orange-3),1);
--semi-color-primary-light-default: rgba(var(--semi-blue-0),1);
--semi-color-success-light-default: rgba(var(--semi-green-0),1);
--semi-color-tertiary-light-hover: rgba(var(--semi-neutral-1),1);
--semi-color-warning-light-default: rgba(var(--semi-orange-0),1);
--semi-color-tertiary-light-active: rgba(var(--semi-neutral-2),1);
--semi-color-secondary-light-hover: rgba(var(--semi-yellow-1),1);
--semi-color-tertiary-light-default: rgba(var(--semi-neutral-0),1);
--semi-color-secondary-light-active: rgba(var(--semi-yellow-2),1);
--semi-color-secondary-light-default: rgba(var(--semi-yellow-0),1);
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
-webkit-font-smoothing: antialiased;;
--semi-shadow-elevated: 0px 0px 1px rgba(0,0,0,0.3),0px 4px 14px rgba(0,0,0,0.1);
--semi-shadow-0: none;
--semi-shadow-1: none;
--semi-shadow-2: 0px 2px 4px rgba(0,0,0,0.14),0px 0px 1px rgba(0,0,0,0.16);
--semi-shadow-knob: 0 4px 6px rgba(0,0,0,0.1),0 0 1px rgba(0,0,0,0.3);
--semi-border-radius-large: 16px;
--semi-border-radius-small: 8px;
--semi-border-radius-circle: 99999px;
--semi-border-radius-medium: 12px;
--semi-border-radius-extra-small: 4px;


}
body[theme-mode="dark"], body .semi-always-dark {
--semi-color-bg-0: rgb(22,22,26);
--semi-color-bg-1: rgb(35,36,41);
--semi-color-bg-2: rgb(53,54,60);
--semi-color-bg-3: rgb(67,68,74);
--semi-color-bg-4: rgb(79,81,89);
--semi-color-info: rgba(var(--semi-blue-5), 1);
--semi-color-link: var(--semi-color-primary);
--semi-color-nav-bg: var(--semi-color-bg-1);
--semi-color-black: rgba(var(--semi-black),1);
--semi-color-data-0: rgba(94, 109, 194, 1);
--semi-color-data-1: rgba(8, 104, 120, 1);
--semi-color-data-2: rgba(250, 173, 63, 1);
--semi-color-data-3: rgba(76, 43, 156, 1);
--semi-color-data-4: rgba(16, 125, 248, 1);
--semi-color-data-5: rgba(248, 202, 16, 1);
--semi-color-data-6: rgba(195, 30, 87, 1);
--semi-color-data-7: rgba(5, 119, 115, 1);
--semi-color-data-8: rgba(154, 207, 13, 1);
--semi-color-data-9: rgba(117, 29, 138, 1);
--semi-color-fill-0: rgba(var(--semi-neutral-8),0.05);
--semi-color-fill-1: rgba(var(--semi-neutral-8),0.09);
--semi-color-fill-2: rgba(var(--semi-neutral-8),0.13);
--semi-color-text-0: rgba(var(--semi-neutral-9),1);
--semi-color-text-1: rgba(var(--semi-neutral-9),0.8);
--semi-color-text-2: rgba(var(--semi-neutral-9),0.6);
--semi-color-text-3: rgba(var(--semi-neutral-9),0.35);
--semi-color-white: #E4E7F5;
--semi-color-data-10: rgba(16, 162, 180, 1);
--semi-color-data-11: rgba(208, 110, 11, 1);
--semi-color-data-12: rgba(113, 66, 197, 1);
--semi-color-data-13: rgba(7, 100, 212, 1);
--semi-color-data-14: rgba(251, 232, 110, 1);
--semi-color-data-15: rgba(160, 19, 73, 1);
--semi-color-data-16: rgba(11, 179, 167, 1);
--semi-color-data-17: rgba(98, 138, 6, 1);
--semi-color-data-18: rgba(162, 48, 179, 1);
--semi-color-data-19: rgba(40, 51, 138, 1);
--semi-color-overlay-bg: rgba(22, 22, 26, 0.6);
--semi-color-border: rgba(var(--semi-neutral-1),1);
--semi-color-danger: rgba(var(--semi-red-5),1);
--semi-color-shadow: rgba(0,0,0,0.04);
--semi-color-border-1: rgba(var(--semi-neutral-2),1);
--semi-color-border-2: rgba(var(--semi-neutral-3),1);
--semi-color-border-3: rgba(var(--semi-neutral-5),1);
--semi-color-default: rgba(var(--semi-neutral-0),1);
--semi-color-info-hover: rgba(var(--semi-blue-6), 1);
--semi-color-link-hover: var(--semi-color-primary-hover);
--semi-color-primary: rgba(var(--semi-blue-5),1);
--semi-color-success: rgba(var(--semi-green-5),1);
--semi-color-warning: rgba(var(--semi-orange-5),1);
--semi-color-info-active: rgba(var(--semi-blue-7), 1);
--semi-color-link-active: var(--semi-color-primary-active);
--semi-color-link-visited: var(--semi-color-primary);
--semi-color-tertiary: rgba(var(--semi-neutral-5),1);
--semi-color-focus-border: rgba(var(--semi-brand-5), 1);
--semi-color-info-disabled: rgba(var(--semi-blue-2), 1);
--semi-color-danger-hover: rgba(var(--semi-red-6),1);
--semi-color-highlight: rgba(var(--semi-white), 1);
--semi-color-secondary: rgba(var(--semi-yellow-5),1);
--semi-color-danger-active: rgba(var(--semi-red-7),1);
--semi-color-disabled-bg: rgba(var(--semi-neutral-2),1);
--semi-color-default-hover: rgba(var(--semi-neutral-1),1);
--semi-color-primary-hover: rgba(var(--semi-blue-4),1);
--semi-color-success-hover: rgba(var(--semi-green-6),1);
--semi-color-warning-hover: rgba(var(--semi-orange-6),1);
--semi-color-default-active: rgba(var(--semi-neutral-2),1);
--semi-color-disabled-fill: rgba(var(--semi-neutral-8),0.04);
--semi-color-disabled-text: rgba(var(--semi-neutral-9),0.35);
--semi-color-highlight-bg: rgba(var(--semi-yellow-2), 1);
--semi-color-primary-active: rgba(var(--semi-blue-6),1);
--semi-color-success-active: rgba(var(--semi-green-7),1);
--semi-color-warning-active: rgba(var(--semi-orange-7),1);
--semi-color-tertiary-hover: rgba(var(--semi-neutral-6),1);
--semi-color-disabled-border: rgba(var(--semi-neutral-9),0.04);
--semi-color-primary-disabled: rgba(var(--semi-blue-2),1);
--semi-color-success-disabled: rgba(var(--semi-green-2),1);
--semi-color-tertiary-active: rgba(var(--semi-neutral-7),1);
--semi-color-secondary-hover: rgba(var(--semi-yellow-6),1);
--semi-color-secondary-active: rgba(var(--semi-yellow-7),1);
--semi-color-info-light-hover: rgba(var(--semi-blue-5), 0.3);
--semi-color-info-light-active: rgba(var(--semi-blue-5), 0.4);
--semi-color-secondary-disabled: rgba(var(--semi-yellow-2),1);
--semi-color-info-light-default: rgba(var(--semi-blue-5), 0.2);
--semi-color-danger-light-focus: rgba(var(--semi-red-6),0.65);
--semi-color-danger-light-hover: rgba(var(--semi-red-6),0.35);
--semi-color-danger-light-active: rgba(var(--semi-red-6),0.5);
--semi-color-danger-light-default: rgba(var(--semi-red-6),0.2);
--semi-color-primary-light-hover: rgba(var(--semi-blue-1),1);
--semi-color-success-light-hover: rgba(var(--semi-green-1),1);
--semi-color-warning-light-focus: rgba(var(--semi-orange-6),0.65);
--semi-color-warning-light-hover: rgba(var(--semi-orange-6),0.35);
--semi-color-primary-light-active: rgba(var(--semi-blue-2),1);
--semi-color-success-light-active: rgba(var(--semi-green-2),1);
--semi-color-warning-light-active: rgba(var(--semi-orange-6),5);
--semi-color-primary-light-default: rgba(var(--semi-blue-0),1);
--semi-color-success-light-default: rgba(var(--semi-green-0),1);
--semi-color-tertiary-light-hover: rgba(var(--semi-neutral-1),1);
--semi-color-warning-light-default: rgba(var(--semi-orange-6),0.2);
--semi-color-tertiary-light-active: rgba(var(--semi-neutral-2),1);
--semi-color-secondary-light-hover: rgba(var(--semi-yellow-1),1);
--semi-color-tertiary-light-default: rgba(var(--semi-neutral-0),1);
--semi-color-secondary-light-active: rgba(var(--semi-yellow-2),1);
--semi-color-secondary-light-default: rgba(var(--semi-yellow-0),1);
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
-webkit-font-smoothing: antialiased;;
--semi-shadow-elevated: 0px 0px 1px rgba(0,0,0,0.3),0px 4px 14px rgba(0,0,0,0.1);
--semi-shadow-0: none;
--semi-shadow-1: none;
--semi-shadow-2: 0px 2px 4px rgba(0,0,0,0.14),0px 0px 1px rgba(0,0,0,0.16);
--semi-shadow-knob: 0 4px 6px rgba(0,0,0,0.1),0 0 1px rgba(0,0,0,0.3);
--semi-border-radius-large: 16px;
--semi-border-radius-small: 8px;
--semi-border-radius-circle: 99999px;
--semi-border-radius-medium: 12px;
--semi-border-radius-extra-small: 4px;


}
