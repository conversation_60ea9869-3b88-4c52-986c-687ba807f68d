# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/), this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html) and to [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/).

## Unreleased changes

### Documentation
- :robot: changelog file generated [`d0c8365b`](https://github.com/Veloera/Veloera/commit/d0c8365b)
- :robot: changelog file generated [`870be6ef`](https://github.com/Veloera/Veloera/commit/870be6ef)
- :robot: changelog file generated [`88aafb02`](https://github.com/Veloera/Veloera/commit/88aafb02)
- :robot: changelog file generated [`46629169`](https://github.com/Veloera/Veloera/commit/46629169)
- :robot: changelog file generated [`474c94f2`](https://github.com/Veloera/Veloera/commit/474c94f2)
- :robot: changelog file generated [`edcead5f`](https://github.com/Veloera/Veloera/commit/edcead5f)
- :robot: changelog file generated [`bd405a7a`](https://github.com/Veloera/Veloera/commit/bd405a7a)
- :robot: changelog file generated [`48f4877a`](https://github.com/Veloera/Veloera/commit/48f4877a)
- :robot: changelog file generated [`ebdd544d`](https://github.com/Veloera/Veloera/commit/ebdd544d)
- :robot: changelog file generated [`8bfb23e3`](https://github.com/Veloera/Veloera/commit/8bfb23e3)
- :robot: changelog file generated [`bbc8bfbd`](https://github.com/Veloera/Veloera/commit/bbc8bfbd)

### Chore
- add copyright header to most source files [`ffc8c43a`](https://github.com/Veloera/Veloera/commit/ffc8c43a)
- add script to update copyright headers in web files [`111a5633`](https://github.com/Veloera/Veloera/commit/111a5633)
- add script to update copyright headers in Go files [`0d843243`](https://github.com/Veloera/Veloera/commit/0d843243)
- delete a useless bin file [`f1337203`](https://github.com/Veloera/Veloera/commit/f1337203)

### Other
- Merge branch 'main' of https://github.com/Veloera/Veloera [`1deb6e68`](https://github.com/Veloera/Veloera/commit/1deb6e68)

## v0.3.29.1 (2025-07-07)

### Chore
- update dependencies and upgrade @visactor packages to latest versions [`077ef9a3`](https://github.com/Veloera/Veloera/commit/077ef9a3)

## v0.3.29 (2025-07-07)

### Feature
- add PR merge readiness check workflow [`9ac574b2`](https://github.com/Veloera/Veloera/commit/9ac574b2)
- add exemption settings (#123) [`cc8a497a`](https://github.com/Veloera/Veloera/commit/cc8a497a)

### Bug Fixes
- fix submit when using key list mode by filtering empty keys and updating input key [`afade86f`](https://github.com/Veloera/Veloera/commit/afade86f)
- upgrade @visactor/react-vchart from 1.8.11 to 1.13.11 (#125) [`29f7c060`](https://github.com/Veloera/Veloera/commit/29f7c060)
- upgrade @visactor/vchart from 1.8.11 to 1.13.11 (#124) [`0e276ca2`](https://github.com/Veloera/Veloera/commit/0e276ca2)
- add custom endpoint support to aws bedrock adapter (#126) [`00b2b0b1`](https://github.com/Veloera/Veloera/commit/00b2b0b1)
- add Redis null check in redemption Redeem function (#121) [`0d9354e8`](https://github.com/Veloera/Veloera/commit/0d9354e8)

### Continuous Integration
- remove merge readiness check workflow [`cae72d2a`](https://github.com/Veloera/Veloera/commit/cae72d2a)
- remove comment from GITHUB_TOKEN line in merge readiness check workflow [`37424cf3`](https://github.com/Veloera/Veloera/commit/37424cf3)

### Documentation
- :robot: changelog file generated [`95570017`](https://github.com/Veloera/Veloera/commit/95570017)

### Chore
- remove add_responses_method.sh script [`52be55d6`](https://github.com/Veloera/Veloera/commit/52be55d6)

### Other
- Merge branch 'main' of https://github.com/Veloera/Veloera [`e72425ef`](https://github.com/Veloera/Veloera/commit/e72425ef)

## v0.3.28.1 (2025-06-28)

### Feature
- add support for GitHub channel and model listing (#113) [`55a54f4d`](https://github.com/Veloera/Veloera/commit/55a54f4d)

## v0.3.28 (2025-06-28)

### Feature
- implement error logging (#112) [`7e55b846`](https://github.com/Veloera/Veloera/commit/7e55b846)

### Documentation
- :robot: changelog file generated [`1af99f86`](https://github.com/Veloera/Veloera/commit/1af99f86)

## v0.3.27.2 (2025-06-28)

### Chore
- switch project license to GPL v3 with additional terms and update related documentation and footer [`07715152`](https://github.com/Veloera/Veloera/commit/07715152)

## v0.3.27.1 (2025-06-26)

### Other
- feature: dd token rate limit feature (#111) [`c956fd33`](https://github.com/Veloera/Veloera/commit/c956fd33)

## v0.3.27 (2025-06-26)

### Feature
- add token rate limit [`4e50d162`](https://github.com/Veloera/Veloera/commit/4e50d162)

### Bug Fixes
- correct in-memory rate limiter key usage in token rate limit handler [`a885eb52`](https://github.com/Veloera/Veloera/commit/a885eb52)
- fix username missing in check-in log (#109) [`199a7afb`](https://github.com/Veloera/Veloera/commit/199a7afb)
- fetch saved model list via channelId in edit mode (#108) [`2a02e1a6`](https://github.com/Veloera/Veloera/commit/2a02e1a6)

### Documentation
- add sponsor section to README.md to acknowledge project supporters [`7e294f7e`](https://github.com/Veloera/Veloera/commit/7e294f7e)

### Style
- remove unused timeFormat constant in token-rate-limit.go [`185c0037`](https://github.com/Veloera/Veloera/commit/185c0037)

### Refactor
- 优化速率限制输入框的标签和布局结构 [`2241614b`](https://github.com/Veloera/Veloera/commit/2241614b)

## v0.3.26 (2025-06-25)

### Feature
- implement conversation logging for gemini channel (#107) [`d6800a71`](https://github.com/Veloera/Veloera/commit/d6800a71)
- support custom start&end date for redeem code (#97) [`8117f302`](https://github.com/Veloera/Veloera/commit/8117f302)

### Bug Fixes
- modify the API address input box prompt to require a trailing / to disable the default /v1 prefix [`52fcc290`](https://github.com/Veloera/Veloera/commit/52fcc290)
- update confirmation message to include used redemptions in deletion prompt [`2bd1f3b5`](https://github.com/Veloera/Veloera/commit/2bd1f3b5)
- delete both disabled and used redemption codes in batch cleanup [`f80e5780`](https://github.com/Veloera/Veloera/commit/f80e5780)
- upgrade lucide-react from 0.503.0 to 0.511.0 (#101) [`89f42f13`](https://github.com/Veloera/Veloera/commit/89f42f13)
- upgrade next from 15.3.1 to 15.3.3 (#102) [`7908b87d`](https://github.com/Veloera/Veloera/commit/7908b87d)

## v0.3.25 (2025-06-15)

### Feature
- add stream_support pseudo streaming (#95) [`a5b82123`](https://github.com/Veloera/Veloera/commit/a5b82123)
- add chat content log switch (#92) [`9a5dcb3c`](https://github.com/Veloera/Veloera/commit/9a5dcb3c)
- block browser extension via global switch (#91) [`f8b51cda`](https://github.com/Veloera/Veloera/commit/f8b51cda)

### Bug Fixes
- disable stream flag if using pseudo stream mode [`bb08a4d4`](https://github.com/Veloera/Veloera/commit/bb08a4d4)
- fix type in gemini pseudo stream (#96) [`e2c3689f`](https://github.com/Veloera/Veloera/commit/e2c3689f)

### Documentation
- add ko-fi link [`7f939052`](https://github.com/Veloera/Veloera/commit/7f939052)
- 补充必选与可选项用于澄清 issue 提交要求 [`9143b84f`](https://github.com/Veloera/Veloera/commit/9143b84f)
- :robot: changelog file generated [`dae1c898`](https://github.com/Veloera/Veloera/commit/dae1c898)

## v0.3.24 (2025-06-09)

### Bug Fixes
- change RedisHDelObj to use Del instead of HDel [`aad6531b`](https://github.com/Veloera/Veloera/commit/aad6531b)
- prevent multiple redemptions of the same code under high concurrency (#88) [`19ffa60c`](https://github.com/Veloera/Veloera/commit/19ffa60c)

## v0.3.23.1 (2025-06-09)

### Bug Fixes
- remove duplicate gin import in relay-gemini.go (#86) [`fc184745`](https://github.com/Veloera/Veloera/commit/fc184745)

## v0.3.23 (2025-06-09)

### Feature
- enhance Gemini relay to support Thought processing (#80) [`0fd7c5a4`](https://github.com/Veloera/Veloera/commit/0fd7c5a4)

### Bug Fixes
- display duplicate models with and without prefixes (#85) [`3f37ad53`](https://github.com/Veloera/Veloera/commit/3f37ad53)
- enhance schema cleaning (#79) [`5c4ce2a1`](https://github.com/Veloera/Veloera/commit/5c4ce2a1)

### Refactor
- format code [`1b74bb95`](https://github.com/Veloera/Veloera/commit/1b74bb95)

### Other
- Merge branch 'main' of https://github.com/Veloera/Veloera [`40a7d134`](https://github.com/Veloera/Veloera/commit/40a7d134)

## v0.3.22 (2025-06-05)

### Feature
- refresh prefix cache when channel changes (#77) [`a23d6c3d`](https://github.com/Veloera/Veloera/commit/a23d6c3d)

### Bug Fixes
- remove deprecated Claude API (#73) [`eaaeb315`](https://github.com/Veloera/Veloera/commit/eaaeb315)
- support multiple channels with different prefixes for same model id without conflict (#70) [`082a7b69`](https://github.com/Veloera/Veloera/commit/082a7b69)

### Documentation
- :robot: changelog file generated [`b69df048`](https://github.com/Veloera/Veloera/commit/b69df048)

### Refactor
- simplify request URL generation logic [`8c445790`](https://github.com/Veloera/Veloera/commit/8c445790)

## v0.3.21.1 (2025-06-01)

### Bug Fixes
- key cannot be cleared in edit channel mode (#68) [`c09c25ba`](https://github.com/Veloera/Veloera/commit/c09c25ba)
- wrongly add ThinkingConfig to normal models (#66) [`dff0f84b`](https://github.com/Veloera/Veloera/commit/dff0f84b)
- fetch_models use wrong apiKey and release.yml cache invalid (#67) [`c93f8cf9`](https://github.com/Veloera/Veloera/commit/c93f8cf9)

## v0.3.21 (2025-06-01)

## v0.3.20 (2025-06-01)

### Feature
- add URL code handling and user quota retrieval [`bf0562e3`](https://github.com/Veloera/Veloera/commit/bf0562e3)
- update navigation to handle return URLs [`54ed4bcd`](https://github.com/Veloera/Veloera/commit/54ed4bcd)
- Add new new customer rebate function (#49) [`9ed91de9`](https://github.com/Veloera/Veloera/commit/9ed91de9)

### Bug Fixes
- encode URL parameters for login redirection [`6b9fde84`](https://github.com/Veloera/Veloera/commit/6b9fde84)
- region global is not working for Vertex AI [`9dfc4abe`](https://github.com/Veloera/Veloera/commit/9dfc4abe)
- fix pop-ups blocked issuse (#63) [`9295addb`](https://github.com/Veloera/Veloera/commit/9295addb)

### Refactor
- remove checkedText from most switches [`0bd43945`](https://github.com/Veloera/Veloera/commit/0bd43945)

## v0.3.19 (2025-05-30)

### Feature
- use a banner to show that online recharge is not enabled [`87de5635`](https://github.com/Veloera/Veloera/commit/87de5635)
- add option to hide upstream error details (#61) [`460990c4`](https://github.com/Veloera/Veloera/commit/460990c4)
- Remember tag aggregation mode state (#56) [`cd38d7e9`](https://github.com/Veloera/Veloera/commit/cd38d7e9)

### Other
- merge: Merge branch 'main' of https://github.com/Veloera/Veloera [`46db3401`](https://github.com/Veloera/Veloera/commit/46db3401)

## v0.3.18 (2025-05-25)

### Feature
- no longer remove the dots from the model name from Azure channel [`7d67a02b`](https://github.com/Veloera/Veloera/commit/7d67a02b)

### Bug Fixes
- add user-level auth for /api/log/token, and make sure the token belongs to that user [`529dc4f4`](https://github.com/Veloera/Veloera/commit/529dc4f4)
- removed unused import of net/textproto [`43bd609a`](https://github.com/Veloera/Veloera/commit/43bd609a)
- fix typo in aws constants [`eb91dabc`](https://github.com/Veloera/Veloera/commit/eb91dabc)

### Continuous Integration
- let changelog workflow no longer run when new tag are pushed [`9bb69a31`](https://github.com/Veloera/Veloera/commit/9bb69a31)

## v0.3.17 (2025-05-24)

### Feature
- Enhance channel refresh and search logic [`88bbdeac`](https://github.com/Veloera/Veloera/commit/88bbdeac)
- support for Claude 4 series [`bd398e1e`](https://github.com/Veloera/Veloera/commit/bd398e1e)

### Bug Fixes
- fix cannot get max user id [`ca8d7645`](https://github.com/Veloera/Veloera/commit/ca8d7645)
- only use thinking adapter for model in Gemini 2.5 series [`fc3334f4`](https://github.com/Veloera/Veloera/commit/fc3334f4)

### Chore
- fix typo (#47) [`7af24d0f`](https://github.com/Veloera/Veloera/commit/7af24d0f)

### Other
- merge: Merge branch 'main' of https://github.com/Veloera/Veloera [`22504d19`](https://github.com/Veloera/Veloera/commit/22504d19)
- revert: revert "feat: add setting to disable chat content recording" [`1dd9bc82`](https://github.com/Veloera/Veloera/commit/1dd9bc82)

## v0.3.16 (2025-05-19)

### Feature
- enhance channel edit page key input and model selection UI [`5b1e6d60`](https://github.com/Veloera/Veloera/commit/5b1e6d60)

### Continuous Integration
- fix permission err [`be854d14`](https://github.com/Veloera/Veloera/commit/be854d14)
- fix branch err [`39e747f2`](https://github.com/Veloera/Veloera/commit/39e747f2)

### Documentation
- :robot: changelog file generated [`a92b34a5`](https://github.com/Veloera/Veloera/commit/a92b34a5)

### Chore
- Merge branch 'main' of https://github.com/Veloera/Veloera [`6fe18323`](https://github.com/Veloera/Veloera/commit/6fe18323)

## v0.3.15 (2025-05-18)

### Bug Fixes
- fix 404 error when trying to call gemini models [`2cb1ce20`](https://github.com/Veloera/Veloera/commit/2cb1ce20)

### Continuous Integration
- add ci to generate changelog [`b50c5699`](https://github.com/Veloera/Veloera/commit/b50c5699)

### Chore
- add link to discussion in issue config [`66155c71`](https://github.com/Veloera/Veloera/commit/66155c71)

## v0.3.14.1 (2025-05-17)

### Bug Fixes
- add missing dep(sass-embedded) for web [`fc83f673`](https://github.com/Veloera/Veloera/commit/fc83f673)

## v0.3.14 (2025-05-17)

### Feature
- update theme [`5139d464`](https://github.com/Veloera/Veloera/commit/5139d464)

### Documentation
- add hero image at readme [`20506072`](https://github.com/Veloera/Veloera/commit/20506072)
- add more details in about section in readme [`ee45d1e4`](https://github.com/Veloera/Veloera/commit/ee45d1e4)
- update about section in readme [`85a8684c`](https://github.com/Veloera/Veloera/commit/85a8684c)

## v0.3.13 (2025-05-16)

### Feature
- support multi-key view for all type of channels [`1deddbe3`](https://github.com/Veloera/Veloera/commit/1deddbe3)
- add setting to disable chat content recording [`91885cfe`](https://github.com/Veloera/Veloera/commit/91885cfe)

### Bug Fixes
- fix gemini default setting [`44ef0957`](https://github.com/Veloera/Veloera/commit/44ef0957)

## v0.3.12.2 (2025-05-14)

### Bug Fixes
- quick fix to package.json [`6d1a4941`](https://github.com/Veloera/Veloera/commit/6d1a4941)

## v0.3.12.1 (2025-05-14)

### Bug Fixes
- fix the unexcepted link: in package.json [`f4f6d816`](https://github.com/Veloera/Veloera/commit/f4f6d816)

## v0.3.12 (2025-05-14)

### Feature
- support custom redeem code [`2c4d94d2`](https://github.com/Veloera/Veloera/commit/2c4d94d2)
- create a dedicated log category for check-ins [`fb50b4d8`](https://github.com/Veloera/Veloera/commit/fb50b4d8)
- Support Stripe as a payment method (#23) [`f85487d0`](https://github.com/Veloera/Veloera/commit/f85487d0)
- Support for gemini thinking suffix [`d76dc7af`](https://github.com/Veloera/Veloera/commit/d76dc7af)
- Support for gemini thinking suffix [`d4af0070`](https://github.com/Veloera/Veloera/commit/d4af0070)
- Option to temp disable multi-key view (GH-18) [`ef3cfcb4`](https://github.com/Veloera/Veloera/commit/ef3cfcb4)
- Support /v1/responses endpoint [`aff7e126`](https://github.com/Veloera/Veloera/commit/aff7e126)
- Support /v1/responses endpoint [`885b9d85`](https://github.com/Veloera/Veloera/commit/885b9d85)
- List view for multi key [`2a27e0c4`](https://github.com/Veloera/Veloera/commit/2a27e0c4)
- Add support for pasting multi-line key [`4c949d3b`](https://github.com/Veloera/Veloera/commit/4c949d3b)

### Bug Fixes
- use reflection to get model name in migration log [`58364b54`](https://github.com/Veloera/Veloera/commit/58364b54)
- Handle PostgreSQL 'relation already exists' error during migration [`65498936`](https://github.com/Veloera/Veloera/commit/65498936)
- Correct misspelling in common/go-channel.go comments [`cc500ef3`](https://github.com/Veloera/Veloera/commit/cc500ef3)
- use latest form values when fetching models in advanced selector #19 [`696985cf`](https://github.com/Veloera/Veloera/commit/696985cf)
- Include prefixed model names in user model list [`e3c416b1`](https://github.com/Veloera/Veloera/commit/e3c416b1)

### Continuous Integration
- Change download-artifact@v3 to v4 [`e1289b50`](https://github.com/Veloera/Veloera/commit/e1289b50)
- Use npm install [`dc1f717f`](https://github.com/Veloera/Veloera/commit/dc1f717f)
- Disable build cache [`5aca6ebb`](https://github.com/Veloera/Veloera/commit/5aca6ebb)
- Update checkout&artifact action version to v4 [`767d78e1`](https://github.com/Veloera/Veloera/commit/767d78e1)

### Documentation
- Add migrate guide [`eee63476`](https://github.com/Veloera/Veloera/commit/eee63476)
- Add whitespace between badges [`c52b112b`](https://github.com/Veloera/Veloera/commit/c52b112b)

### Chore
- Delete useless files [`755bea8c`](https://github.com/Veloera/Veloera/commit/755bea8c)
- Rename to veloera [`baab9fb9`](https://github.com/Veloera/Veloera/commit/baab9fb9)
- Rename to Veloera [`5df1fc6a`](https://github.com/Veloera/Veloera/commit/5df1fc6a)
- Add aider files to gitignore [`56f6c089`](https://github.com/Veloera/Veloera/commit/56f6c089)

### Refactor
- reduce complexity of ResponsesHelper function [`e5bd366e`](https://github.com/Veloera/Veloera/commit/e5bd366e)
- Update check-in logic to use UTC for date comparisons [`83592237`](https://github.com/Veloera/Veloera/commit/83592237)

### Other
- Revert "feat: Support Stripe as a payment method (#23)" (#30) [`c1fba255`](https://github.com/Veloera/Veloera/commit/c1fba255)
- format: Format code using gofmt [`abdb966a`](https://github.com/Veloera/Veloera/commit/abdb966a)
- format: Format code use gofmt [`********`](https://github.com/Veloera/Veloera/commit/********)
- Fix workflow badge path in README [`123fc658`](https://github.com/Veloera/Veloera/commit/123fc658)
- Simplify CI [`e030c702`](https://github.com/Veloera/Veloera/commit/e030c702)

## v0.3.6 (2025-04-24)

### Feature
- Add advanced model fetch [`a3f2f514`](https://github.com/Veloera/Veloera/commit/a3f2f514)

### Chore
- Fix proxy path in Vite config [`fd7265c6`](https://github.com/Veloera/Veloera/commit/fd7265c6)
- Working on new interface [`664017a5`](https://github.com/Veloera/Veloera/commit/664017a5)

## v0.3.5 (2025-04-23)

### Feature
- Migrate databases from one-api.db to veloera.db [`79b1ca51`](https://github.com/Veloera/Veloera/commit/79b1ca51)

### Bug Fixes
- Fix username confilct [`62142698`](https://github.com/Veloera/Veloera/commit/62142698)

## v0.3.4 (2025-04-22)

### Feature
- Implement round-robin key selection [`3ab82c44`](https://github.com/Veloera/Veloera/commit/3ab82c44)

### Bug Fixes
- No longer accept multi-key in Vertex channel [`fdd609c3`](https://github.com/Veloera/Veloera/commit/fdd609c3)

### Style
- Format code using gofmt [`270a37f4`](https://github.com/Veloera/Veloera/commit/270a37f4)

## v0.3.3 (2025-04-22)

### Feature
- Channel prefix [`8f28acc5`](https://github.com/Veloera/Veloera/commit/8f28acc5)
- Add daily check-in feature [`78bfeda9`](https://github.com/Veloera/Veloera/commit/78bfeda9)
- Add CheckIn settings component [`8ba4a540`](https://github.com/Veloera/Veloera/commit/8ba4a540)

### Bug Fixes
- Cannot save check in settings [`8996d68a`](https://github.com/Veloera/Veloera/commit/8996d68a)

## v0.3.2-beta (2025-04-21)

### Feature
- Add check-in [`670c668b`](https://github.com/Veloera/Veloera/commit/670c668b)
- Add batch operations for redemptions [`0221ea63`](https://github.com/Veloera/Veloera/commit/0221ea63)
- Add check-in [`29b3e0c2`](https://github.com/Veloera/Veloera/commit/29b3e0c2)

### Documentation
- Add cool badges in README [`f0df9d66`](https://github.com/Veloera/Veloera/commit/f0df9d66)
- Add CLAUDE.md [`b5fac30a`](https://github.com/Veloera/Veloera/commit/b5fac30a)

### Chore
- Delete patch files [`4946af08`](https://github.com/Veloera/Veloera/commit/4946af08)

### Other
- format: Format code [`125396b2`](https://github.com/Veloera/Veloera/commit/125396b2)

## v0.3.1 (2025-04-20)

## v0.3.0 (2025-04-20)

### Feature
- Show chat content and context [`ced6b0ba`](https://github.com/Veloera/Veloera/commit/ced6b0ba)
- More detail in LogsTable [`fa503645`](https://github.com/Veloera/Veloera/commit/fa503645)
- Avoid charging for empty responses [`649ae76b`](https://github.com/Veloera/Veloera/commit/649ae76b)
- Empty reply now does not count as usage [`9872cd86`](https://github.com/Veloera/Veloera/commit/9872cd86)
- Remove the banner at general setting [`05f9b131`](https://github.com/Veloera/Veloera/commit/05f9b131)
- Update loading effect [`679e6730`](https://github.com/Veloera/Veloera/commit/679e6730)
- Only show the chat button when the chat link is configed [`8fe3817e`](https://github.com/Veloera/Veloera/commit/8fe3817e)
- Show channel name at LogsTable [`c66f684a`](https://github.com/Veloera/Veloera/commit/c66f684a)
- Add refresh button for LogsTable [`3abe72f0`](https://github.com/Veloera/Veloera/commit/3abe72f0)

### Bug Fixes
- Fix missing VERSION file which is required by Dockerfile [`f93e2f94`](https://github.com/Veloera/Veloera/commit/f93e2f94)
- Fixed display error when success redeem a code [`4099b331`](https://github.com/Veloera/Veloera/commit/4099b331)

### Documentation
- Sync features section with latest features [`8012db8b`](https://github.com/Veloera/Veloera/commit/8012db8b)
- Add features section at README [`c35392a6`](https://github.com/Veloera/Veloera/commit/c35392a6)

### Other
- revert: 9872cd86 [`7a0604f4`](https://github.com/Veloera/Veloera/commit/7a0604f4)
- revert: Revert changes in commit f258225 [`cf60bb1d`](https://github.com/Veloera/Veloera/commit/cf60bb1d)

## v0.2.1 (2025-04-17)

### Feature
- Let the channel key visible and editable at frontend [`260ab856`](https://github.com/Veloera/Veloera/commit/260ab856)
- Start working on the new UI [`025a4d72`](https://github.com/Veloera/Veloera/commit/025a4d72)
- Auto re-order channel id when delete channel [`f2582252`](https://github.com/Veloera/Veloera/commit/f2582252)

### Bug Fixes
- Fix sometimes max_uses will be string [`b3f7bdbf`](https://github.com/Veloera/Veloera/commit/b3f7bdbf)
- Fix a little typo in EditUser [`7bcec0d2`](https://github.com/Veloera/Veloera/commit/7bcec0d2)
- Typo in docker-compose.yml [`a27c86a7`](https://github.com/Veloera/Veloera/commit/a27c86a7)

### Documentation
- Replace most new-api with Veloera [`4c03f368`](https://github.com/Veloera/Veloera/commit/4c03f368)

### Chore
- Make session more stable [`3deccb02`](https://github.com/Veloera/Veloera/commit/3deccb02)

## v0.2 (2025-04-17)

### Documentation
- Update README [`8d02adee`](https://github.com/Veloera/Veloera/commit/8d02adee)

## v0.1.2-alpha (2025-04-17)

### Continuous Integration
- Let CI only push to ghcr.io [`1b510d19`](https://github.com/Veloera/Veloera/commit/1b510d19)
- Correct the filename in linux_release [`acf20b08`](https://github.com/Veloera/Veloera/commit/acf20b08)

## v0.1.0 (2025-04-17)

## 0.3.11 (2025-05-05)

### Feature
- Option to temp disable multi-key view (GH-18) [`ef3cfcb4`](https://github.com/Veloera/Veloera/commit/ef3cfcb4)

### Bug Fixes
- use latest form values when fetching models in advanced selector #19 [`696985cf`](https://github.com/Veloera/Veloera/commit/696985cf)

### Chore
- Delete useless files [`755bea8c`](https://github.com/Veloera/Veloera/commit/755bea8c)

## 0.3.10 (2025-05-04)

### Feature
- Support /v1/responses endpoint [`aff7e126`](https://github.com/Veloera/Veloera/commit/aff7e126)
- Support /v1/responses endpoint [`885b9d85`](https://github.com/Veloera/Veloera/commit/885b9d85)

### Chore
- Rename to veloera [`baab9fb9`](https://github.com/Veloera/Veloera/commit/baab9fb9)

### Refactor
- reduce complexity of ResponsesHelper function [`e5bd366e`](https://github.com/Veloera/Veloera/commit/e5bd366e)
- Update check-in logic to use UTC for date comparisons [`83592237`](https://github.com/Veloera/Veloera/commit/83592237)

## 0.3.9 (2025-05-01)

### Feature
- List view for multi key [`2a27e0c4`](https://github.com/Veloera/Veloera/commit/2a27e0c4)
- Add support for pasting multi-line key [`4c949d3b`](https://github.com/Veloera/Veloera/commit/4c949d3b)

## 0.3.8 (2025-04-28)

### Continuous Integration
- Change download-artifact@v3 to v4 [`e1289b50`](https://github.com/Veloera/Veloera/commit/e1289b50)
- Use npm install [`dc1f717f`](https://github.com/Veloera/Veloera/commit/dc1f717f)
- Disable build cache [`5aca6ebb`](https://github.com/Veloera/Veloera/commit/5aca6ebb)

## 0.3.7.1 (2025-04-28)

### Continuous Integration
- Update checkout&artifact action version to v4 [`767d78e1`](https://github.com/Veloera/Veloera/commit/767d78e1)

## 0.3.7 (2025-04-28)

### Feature
- Add advanced model fetch [`a3f2f514`](https://github.com/Veloera/Veloera/commit/a3f2f514)
- Migrate databases from one-api.db to veloera.db [`79b1ca51`](https://github.com/Veloera/Veloera/commit/79b1ca51)
- Implement round-robin key selection [`3ab82c44`](https://github.com/Veloera/Veloera/commit/3ab82c44)
- Channel prefix [`8f28acc5`](https://github.com/Veloera/Veloera/commit/8f28acc5)
- Add daily check-in feature [`78bfeda9`](https://github.com/Veloera/Veloera/commit/78bfeda9)
- Add CheckIn settings component [`8ba4a540`](https://github.com/Veloera/Veloera/commit/8ba4a540)
- Add check-in [`670c668b`](https://github.com/Veloera/Veloera/commit/670c668b)
- Add batch operations for redemptions [`0221ea63`](https://github.com/Veloera/Veloera/commit/0221ea63)
- Add check-in [`29b3e0c2`](https://github.com/Veloera/Veloera/commit/29b3e0c2)
- Show chat content and context [`ced6b0ba`](https://github.com/Veloera/Veloera/commit/ced6b0ba)
- More detail in LogsTable [`fa503645`](https://github.com/Veloera/Veloera/commit/fa503645)
- Avoid charging for empty responses [`649ae76b`](https://github.com/Veloera/Veloera/commit/649ae76b)
- Empty reply now does not count as usage [`9872cd86`](https://github.com/Veloera/Veloera/commit/9872cd86)
- Remove the banner at general setting [`05f9b131`](https://github.com/Veloera/Veloera/commit/05f9b131)
- Update loading effect [`679e6730`](https://github.com/Veloera/Veloera/commit/679e6730)
- Only show the chat button when the chat link is configed [`8fe3817e`](https://github.com/Veloera/Veloera/commit/8fe3817e)
- Show channel name at LogsTable [`c66f684a`](https://github.com/Veloera/Veloera/commit/c66f684a)
- Add refresh button for LogsTable [`3abe72f0`](https://github.com/Veloera/Veloera/commit/3abe72f0)
- Let the channel key visible and editable at frontend [`260ab856`](https://github.com/Veloera/Veloera/commit/260ab856)
- Start working on the new UI [`025a4d72`](https://github.com/Veloera/Veloera/commit/025a4d72)
- Auto re-order channel id when delete channel [`f2582252`](https://github.com/Veloera/Veloera/commit/f2582252)
- Add regex support for sensitive word detect [`b97ec2d5`](https://github.com/Veloera/Veloera/commit/b97ec2d5)
- Add support for /hf/v1 prefix` [`62a88eae`](https://github.com/Veloera/Veloera/commit/62a88eae)
- support gemini output text and inline images. (close #866) [`473e8e0e`](https://github.com/Veloera/Veloera/commit/473e8e0e)
- 添加流模式下的SSE保活机制 #945 [`2f3acd9d`](https://github.com/Veloera/Veloera/commit/2f3acd9d)
- enhance Claude to OpenAI request conversion with additional relay info support [`90576d02`](https://github.com/Veloera/Veloera/commit/90576d02)
- 完善openai转claude支持 [`4b3e30e6`](https://github.com/Veloera/Veloera/commit/4b3e30e6)
- enhance file handling and logging in the application [`cca9c047`](https://github.com/Veloera/Veloera/commit/cca9c047)
- implement parameter cleaning for Gemini functions [`2ec45656`](https://github.com/Veloera/Veloera/commit/2ec45656)
- support zhipu_4v embeddings path [`a4fb3395`](https://github.com/Veloera/Veloera/commit/a4fb3395)
- add xAI handling and response processing [`8723e3f2`](https://github.com/Veloera/Veloera/commit/8723e3f2)
- update adaptor methods and add new image model [`700c05b8`](https://github.com/Veloera/Veloera/commit/700c05b8)
- add xai grok-3-mini reasoning effort [`c5103237`](https://github.com/Veloera/Veloera/commit/c5103237)
- add xai channel [`f500eb17`](https://github.com/Veloera/Veloera/commit/f500eb17)
- Add CheckSetup function call in main to ensure proper initialization #942 [`********`](https://github.com/Veloera/Veloera/commit/********)
- Integrate SetupCheck component for improved setup validation in routing [`5813ca78`](https://github.com/Veloera/Veloera/commit/5813ca78)
- Initialize model settings and improve concurrency control in operation settings [`aa34c303`](https://github.com/Veloera/Veloera/commit/aa34c303)
- Add concurrency control to group ratio management with mutexes [`fb9f5950`](https://github.com/Veloera/Veloera/commit/fb9f5950)
- e5baa6ee ✨ feat: Enhance ModelSettingsVisualEditor with pricing modes and improved model management features [`e5baa6ee`](https://github.com/Veloera/Veloera/commit/e5baa6ee)
- 9207d729 ✨ feat: Add new localization strings for system initialization [`9207d729`](https://github.com/Veloera/Veloera/commit/9207d729)
- Add timestamp and version to setup initialization in PostSetup function [`454dac17`](https://github.com/Veloera/Veloera/commit/454dac17)
- 5fa64624 ✨ feat: Refine personal mode description in setup page for clarity [`5fa64624`](https://github.com/Veloera/Veloera/commit/5fa64624)
- a882e680 ✨ feat: Implement system setup functionality [`a882e680`](https://github.com/Veloera/Veloera/commit/a882e680)
- c418d9ed ✨ feat: Enhance user settings and notification options [`c418d9ed`](https://github.com/Veloera/Veloera/commit/c418d9ed)
- 3c2a86f9 ✨ feat: Update option handling in SystemSetting [`3c2a86f9`](https://github.com/Veloera/Veloera/commit/3c2a86f9)
- Add Parameters Override [`1b072821`](https://github.com/Veloera/Veloera/commit/1b072821)
- Add new cache ratios for o3-mini and gpt-4.5-preview models [`a378665b`](https://github.com/Veloera/Veloera/commit/a378665b)
- Enhance GetCompletionRatio function [`58525c57`](https://github.com/Veloera/Veloera/commit/58525c57)
- Add support for cross-region AWS model handling in awsStreamHandler [`7143b0f1`](https://github.com/Veloera/Veloera/commit/7143b0f1)
- Enhance ConvertClaudeRequest method to set request model and handle vertex-specific request conversion [`19935ee8`](https://github.com/Veloera/Veloera/commit/19935ee8)
- Update RerankerInfo structure and modify GenRelayInfoRerank function to accept RerankRequest [`6fef5aaf`](https://github.com/Veloera/Veloera/commit/6fef5aaf)
- support xinference rerank to jina format [`d1c62a58`](https://github.com/Veloera/Veloera/commit/d1c62a58)
- Introduce JSON decoding utility functions and update error handling in Claude and OpenAI response structures [`b3b1c803`](https://github.com/Veloera/Veloera/commit/b3b1c803)
- Add warning modal for base URL input and display warning banner for specific channel type in EditChannel component [`54e73894`](https://github.com/Veloera/Veloera/commit/54e73894)
- support dify upload image file [`dd393cd0`](https://github.com/Veloera/Veloera/commit/dd393cd0)
- support AWS Model CrossRegion [`892d014c`](https://github.com/Veloera/Veloera/commit/892d014c)
- Add HasSentThinkingContent field to ThinkingContentInfo struct [`9a78db84`](https://github.com/Veloera/Veloera/commit/9a78db84)
- 初步兼容流模式下openai渠道类型转为claude格式访问 #862 [`7e46d421`](https://github.com/Veloera/Veloera/commit/7e46d421)
- claude relay [`bd48f434`](https://github.com/Veloera/Veloera/commit/bd48f434)
- Support postgresql:// dsn format [`c47d8a10`](https://github.com/Veloera/Veloera/commit/c47d8a10)
- Add Xinference channel support [`a981e107`](https://github.com/Veloera/Veloera/commit/a981e107)
- Improve model testing button layout and styling [`1a2bf8df`](https://github.com/Veloera/Veloera/commit/1a2bf8df)
- Enhance error handling with optional detailed error messages [`1819c4d5`](https://github.com/Veloera/Veloera/commit/1819c4d5)
- Add pass-through request option for global settings [`6f24dddc`](https://github.com/Veloera/Veloera/commit/6f24dddc)
- add 'Document Link' option i18n support [`5259acfa`](https://github.com/Veloera/Veloera/commit/5259acfa)
- add oidc support [`c433af28`](https://github.com/Veloera/Veloera/commit/c433af28)
- Improve route handling and dynamic chat navigation in SiderBar [`2af05c16`](https://github.com/Veloera/Veloera/commit/2af05c16)
- gemini Embeddings support [`e1b9f164`](https://github.com/Veloera/Veloera/commit/e1b9f164)
- Enhance mobile UI responsiveness and layout for ChannelsTable and SiderBar [`49bfd2b7`](https://github.com/Veloera/Veloera/commit/49bfd2b7)
- Introduce configurable docs link and remove hardcoded chat links [`00c2d6c1`](https://github.com/Veloera/Veloera/commit/00c2d6c1)
- Improve decimal precision for quota and payment calculations [`68097c13`](https://github.com/Veloera/Veloera/commit/68097c13)
- Add column visibility settings for Channels and Logs tables [`7fcb14e2`](https://github.com/Veloera/Veloera/commit/7fcb14e2)
- update readme and i18n [`3ad96d3b`](https://github.com/Veloera/Veloera/commit/3ad96d3b)
- Add prompt cache hit tokens support for DeepSeek channel #406 [`a9bfcb0d`](https://github.com/Veloera/Veloera/commit/a9bfcb0d)
- Implement cache token ratio for more precise token pricing [`4f194f4e`](https://github.com/Veloera/Veloera/commit/4f194f4e)
- Enhance channel status update with success tracking and dynamic notification #812 [`7f74a966`](https://github.com/Veloera/Veloera/commit/7f74a966)
- Add context-aware goroutine pool for safer concurrent operations [`cbdf26bf`](https://github.com/Veloera/Veloera/commit/cbdf26bf)
- Improve image download and validation in GetImageFromUrl [`6ecfb81c`](https://github.com/Veloera/Veloera/commit/6ecfb81c)
- yanjingxia [`254c25c2`](https://github.com/Veloera/Veloera/commit/254c25c2)
- Add model testing modal with search functionality in ChannelsTable [`8731a32e`](https://github.com/Veloera/Veloera/commit/8731a32e)
- Persist models expanded state in PersonalSetting component [`816e831a`](https://github.com/Veloera/Veloera/commit/816e831a)
- Enhance update checking and system information display [`a3ceae4a`](https://github.com/Veloera/Veloera/commit/a3ceae4a)
- Add self-use mode and demo site mode indicators to HeaderBar [`eb163d9c`](https://github.com/Veloera/Veloera/commit/eb163d9c)
- Add translations for self-use mode and demo site mode settings [`bb300d19`](https://github.com/Veloera/Veloera/commit/bb300d19)
- Add self-use mode for model ratio and price configuration [`7dbb6b01`](https://github.com/Veloera/Veloera/commit/7dbb6b01)
- Add new model management features [`18d3706f`](https://github.com/Veloera/Veloera/commit/18d3706f)
- add new GPT-4.5 preview model ratios [`d6fd50e3`](https://github.com/Veloera/Veloera/commit/d6fd50e3)
- Enhance Claude default max tokens configuration [`cfd3f6c0`](https://github.com/Veloera/Veloera/commit/cfd3f6c0)
- Implement model-specific headers configuration for Claude [`45c56b5d`](https://github.com/Veloera/Veloera/commit/45c56b5d)
- Enhance Claude MaxTokens configuration handling [`d0bc8d17`](https://github.com/Veloera/Veloera/commit/d0bc8d17)
- Refactor model configuration management with new config system [`929668be`](https://github.com/Veloera/Veloera/commit/929668be)
- Add Claude model configuration management #791 [`06a78f90`](https://github.com/Veloera/Veloera/commit/06a78f90)
- Add Jina reranking support for OpenAI adaptor [`287caf8e`](https://github.com/Veloera/Veloera/commit/287caf8e)
- Add Gemini version settings configuration support (close #568) [`bf80d71d`](https://github.com/Veloera/Veloera/commit/bf80d71d)
- Add Gemini safety settings configuration support (close #703) [`e19b244e`](https://github.com/Veloera/Veloera/commit/e19b244e)
- Update Claude relay temperature setting [`f4512688`](https://github.com/Veloera/Veloera/commit/f4512688)
- redis poolsize [`ccf13d44`](https://github.com/Veloera/Veloera/commit/ccf13d44)
- Add support for Claude thinking parameter in request [`83feb492`](https://github.com/Veloera/Veloera/commit/83feb492)
- Add Claude 3.7 Sonnet thinking mode support [`4f212be4`](https://github.com/Veloera/Veloera/commit/4f212be4)
- Add Claude 3.7 Sonnet model to AWS channel mapping [`92918e37`](https://github.com/Veloera/Veloera/commit/92918e37)
- Add support for Claude 3.7 Sonnet model [`de155515`](https://github.com/Veloera/Veloera/commit/de155515)
- Support max_tokens parameter for Ollama channel #782 [`a81a28b7`](https://github.com/Veloera/Veloera/commit/a81a28b7)
- Add model rate limit settings in system configuration [`e9ba392a`](https://github.com/Veloera/Veloera/commit/e9ba392a)
- Add model request rate limiting functionality [`83a37e46`](https://github.com/Veloera/Veloera/commit/83a37e46)
- Add support for different Dify bot types and request URLs [`b6f95dca`](https://github.com/Veloera/Veloera/commit/b6f95dca)
- Enhance token counting and content parsing for messages [`7ff4cebd`](https://github.com/Veloera/Veloera/commit/7ff4cebd)
- Add thinking-to-content option in channel extra settings #780 [`d5ab7d2d`](https://github.com/Veloera/Veloera/commit/d5ab7d2d)
- Add thinking-to-content conversion for stream responses [`115a181d`](https://github.com/Veloera/Veloera/commit/115a181d)
- Add reasoning content support in OpenAI response handling [`6e7587ab`](https://github.com/Veloera/Veloera/commit/6e7587ab)
- Enhance sensitive word detection with detailed logging [`9cc6385b`](https://github.com/Veloera/Veloera/commit/9cc6385b)
- Add base URL input with localized tooltip for channel configuration [`94736407`](https://github.com/Veloera/Veloera/commit/94736407)
- Add localization for notification and webhook settings [`de859c3c`](https://github.com/Veloera/Veloera/commit/de859c3c)
- Improve mobile text truncation and sidebar visibility [`4ce12ea6`](https://github.com/Veloera/Veloera/commit/4ce12ea6)
- Improve image handling for Ollama channels [`971aea09`](https://github.com/Veloera/Veloera/commit/971aea09)
- Enhance Ollama channel support with additional request parameters #771 [`a4b2b9c9`](https://github.com/Veloera/Veloera/commit/a4b2b9c9)
- Implement comprehensive webhook notification system [`4e871507`](https://github.com/Veloera/Veloera/commit/4e871507)
- Implement notification rate limiting mechanism [`56f6b2ab`](https://github.com/Veloera/Veloera/commit/56f6b2ab)
- Add user notification settings with quota warning and multiple notification methods [`3da13448`](https://github.com/Veloera/Veloera/commit/3da13448)
- add Gemini Imagen image generation support [`61d2a2f9`](https://github.com/Veloera/Veloera/commit/61d2a2f9)
- Add support for DeepSeek completions endpoint [`7b384cb9`](https://github.com/Veloera/Veloera/commit/7b384cb9)
- add 火山引擎 support stream options [`e7e5a167`](https://github.com/Veloera/Veloera/commit/e7e5a167)
- Enhance VolcEngine channel support with bot model routing (fix #757) [`6bf99f21`](https://github.com/Veloera/Veloera/commit/6bf99f21)
- Add automatic channel disabling based on configurable keywords [`9edb9f7a`](https://github.com/Veloera/Veloera/commit/9edb9f7a)
- Add invite link banner for specific channel type [`6b923ef7`](https://github.com/Veloera/Veloera/commit/6b923ef7)
- Improve embedding request handling and support across channels [`f5e3063f`](https://github.com/Veloera/Veloera/commit/f5e3063f)
- Add Baidu Qianfan V2 channel support #725 [`eceb6afc`](https://github.com/Veloera/Veloera/commit/eceb6afc)
- Add support for VolcEngine (Doubao) channel #313 #734 [`28c13e5a`](https://github.com/Veloera/Veloera/commit/28c13e5a)
- enhance OpenAI request and response DTOs [`1f527ffc`](https://github.com/Veloera/Veloera/commit/1f527ffc)
- enhance session store security and configuration [`cb4d40c3`](https://github.com/Veloera/Veloera/commit/cb4d40c3)
- configure session store options for API routes [`6acc37cf`](https://github.com/Veloera/Veloera/commit/6acc37cf)
- add `Suffix` to GeneralOpenAIRequest in order to support FIM [`80829051`](https://github.com/Veloera/Veloera/commit/80829051)
- modify channel model_mapping column type to TEXT [`675e62d8`](https://github.com/Veloera/Veloera/commit/675e62d8)
- add SOCKS5 proxy authentication support [`0f5c090a`](https://github.com/Veloera/Veloera/commit/0f5c090a)
- add demo site configuration flag [`a0fe5270`](https://github.com/Veloera/Veloera/commit/a0fe5270)
- add Azure default API version configuration [`187c3361`](https://github.com/Veloera/Veloera/commit/187c3361)
- enhance model name handling and logging [`c68ea565`](https://github.com/Veloera/Veloera/commit/c68ea565)
- add reasoning effort logging and display [`834ceda8`](https://github.com/Veloera/Veloera/commit/834ceda8)
- add reasoning effort configuration for models [`d5746ac3`](https://github.com/Veloera/Veloera/commit/d5746ac3)
- add other_setting docs link [`0831ba26`](https://github.com/Veloera/Veloera/commit/0831ba26)
- support channel request proxy [`cf63ab59`](https://github.com/Veloera/Veloera/commit/cf63ab59)
- add support for o3-mini models in model ratio and request handling [`69102d14`](https://github.com/Veloera/Veloera/commit/69102d14)
- enhance model ratio lookup with case-insensitive and direct matching [`2aca637b`](https://github.com/Veloera/Veloera/commit/2aca637b)
- add chat link for AIaW [`324d127a`](https://github.com/Veloera/Veloera/commit/324d127a)
- 更新模型和模型倍率 [`6e2c8710`](https://github.com/Veloera/Veloera/commit/6e2c8710)
- modify fetching model list in add channel to fetch by type [`f5be2868`](https://github.com/Veloera/Veloera/commit/f5be2868)
- add channel balance for siliconflow and deepseek [`fc33f2f0`](https://github.com/Veloera/Veloera/commit/fc33f2f0)
- support gpt-4o-mini-realtime-preview [`4a0a841e`](https://github.com/Veloera/Veloera/commit/4a0a841e)
- implement pagination and total count for redemptions API #386 [`4f196a62`](https://github.com/Veloera/Veloera/commit/4f196a62)
- enhance user search functionality with pagination support [`014fb7ed`](https://github.com/Veloera/Veloera/commit/014fb7ed)
- enhance user management and pagination features #518 [`be0b2f6a`](https://github.com/Veloera/Veloera/commit/be0b2f6a)
- enhance environment variable handling and security features [`2f01a212`](https://github.com/Veloera/Veloera/commit/2f01a212)
- add multi-file type support for Gemini and Claude [`2b38e8ed`](https://github.com/Veloera/Veloera/commit/2b38e8ed)
- update o1 default token encoder [`d2297d27`](https://github.com/Veloera/Veloera/commit/d2297d27)
- support azure stream_options [`62ae46b5`](https://github.com/Veloera/Veloera/commit/62ae46b5)
- Implement batch tagging functionality for channels [`72d6898e`](https://github.com/Veloera/Veloera/commit/72d6898e)
- Enhance pricing functionality with user group support [`8129aa76`](https://github.com/Veloera/Veloera/commit/8129aa76)
- Update localization and enhance token editing functionality [`fb8595da`](https://github.com/Veloera/Veloera/commit/fb8595da)
- Add FetchModels endpoint and refactor FetchUpstreamModels [`93cda60d`](https://github.com/Veloera/Veloera/commit/93cda60d)
- Enhance LogsTable component with mobile support and date handling improvements [`2ec5eafb`](https://github.com/Veloera/Veloera/commit/2ec5eafb)
- Enhance logging functionality with group support [`7180e6f1`](https://github.com/Veloera/Veloera/commit/7180e6f1)
- Add request start time context key and update middleware [`f3f1817a`](https://github.com/Veloera/Veloera/commit/f3f1817a)
- Enhance GeminiChatHandler to include RelayInfo [`58fac129`](https://github.com/Veloera/Veloera/commit/58fac129)
- Add FunctionResponse type and enhance GeminiPart structure [`1d0ef89c`](https://github.com/Veloera/Veloera/commit/1d0ef89c)
- Introduce settings package and refactor constants [`a7e1d17c`](https://github.com/Veloera/Veloera/commit/a7e1d17c)
- Enhance Gemini function parameter handling [`53ab2aae`](https://github.com/Veloera/Veloera/commit/53ab2aae)
- Enhance LogsTable to render group information [`a5c48c27`](https://github.com/Veloera/Veloera/commit/a5c48c27)
- Add log information generation and enhance LogsTable component [`cffaf0d6`](https://github.com/Veloera/Veloera/commit/cffaf0d6)
- support for Gemini structured output. [`43a7b59b`](https://github.com/Veloera/Veloera/commit/43a7b59b)
- 适配o1模型 [`1fa478af`](https://github.com/Veloera/Veloera/commit/1fa478af)
- 适配o1模型 [`0b1ba2ee`](https://github.com/Veloera/Veloera/commit/0b1ba2ee)
- 适配o1模型 [`35277f2b`](https://github.com/Veloera/Veloera/commit/35277f2b)
- Add GEMINI_VISION_MAX_IMAGE_NUM configuration [`f9a7f608`](https://github.com/Veloera/Veloera/commit/f9a7f608)
- Add new experimental Gemini versions to ModelList [`cab30014`](https://github.com/Veloera/Veloera/commit/cab30014)
- support gemini-2.0-flash-thinking #639 #637 [`9a54b345`](https://github.com/Veloera/Veloera/commit/9a54b345)
- Enhance Home component to support language messaging [`fe6f3d79`](https://github.com/Veloera/Veloera/commit/fe6f3d79)
- Enhance HeaderBar to support language change messaging [`fd86de19`](https://github.com/Veloera/Veloera/commit/fd86de19)
- support gemini SystemInstructions #408 [`e6c6bbde`](https://github.com/Veloera/Veloera/commit/e6c6bbde)
- Add collapsible section for available models in PersonalSettings [`e8444edc`](https://github.com/Veloera/Veloera/commit/e8444edc)
- implement channel settings configuration [`f2809917`](https://github.com/Veloera/Veloera/commit/f2809917)
- Enhance Operation Settings with Group and Model Ratio Management [`a4c43bb8`](https://github.com/Veloera/Veloera/commit/a4c43bb8)
- Refactor App and ChannelsTable components for improved i18n support [`41a7cee9`](https://github.com/Veloera/Veloera/commit/41a7cee9)
- Enhance i18n support in Home component and update translations [`68b87736`](https://github.com/Veloera/Veloera/commit/68b87736)
- Implement status loading in App component and refactor SiderBar [`b86aeb91`](https://github.com/Veloera/Veloera/commit/b86aeb91)
- support i18n [`5f06feb9`](https://github.com/Veloera/Veloera/commit/5f06feb9)
- Integrate i18n support and enhance UI text localization [`221d7b5c`](https://github.com/Veloera/Veloera/commit/221d7b5c)
- 增加价格和倍率的互斥验证，优化模型名称输入提示 [`3587f2c6`](https://github.com/Veloera/Veloera/commit/3587f2c6)
- 优化模型设置可视化编辑器，增强输入验证和提示信息 [`498590d9`](https://github.com/Veloera/Veloera/commit/498590d9)
- 添加保存功能并优化模型数据提交逻辑 [`8504e072`](https://github.com/Veloera/Veloera/commit/8504e072)
- 添加模型设置可视化编辑器组件 [`369ecf36`](https://github.com/Veloera/Veloera/commit/369ecf36)
- init i18n [`cd21aa1c`](https://github.com/Veloera/Veloera/commit/cd21aa1c)
- 兼容OpenAI格式下设置gemini模型联网搜索 #615 [`5d338337`](https://github.com/Veloera/Veloera/commit/5d338337)
- add model gemini-2.0-flash-exp [`b1fb5956`](https://github.com/Veloera/Veloera/commit/b1fb5956)
- Enhance group label display in Playground component [`44512d3c`](https://github.com/Veloera/Veloera/commit/44512d3c)
- Enhance quota data handling and CSS styling [`6625563f`](https://github.com/Veloera/Veloera/commit/6625563f)
- Update SiderBar and Detail components for improved navigation and data visualization [`b2d36b94`](https://github.com/Veloera/Veloera/commit/b2d36b94)
- Enhance color mapping and chart rendering in Detail component [`ab4c9fdb`](https://github.com/Veloera/Veloera/commit/ab4c9fdb)
- Add pricing link to HeaderBar component [`f0d9c896`](https://github.com/Veloera/Veloera/commit/f0d9c896)
- Refactor style management for inner padding in layout components [`28fa77cc`](https://github.com/Veloera/Veloera/commit/28fa77cc)
- Update model lists and enhance model retrieval in Adaptor [`024cdb08`](https://github.com/Veloera/Veloera/commit/024cdb08)
- Add filtering and search functionality to model selection in EditChannel and EditTagModal [`89136dfa`](https://github.com/Veloera/Veloera/commit/89136dfa)
- Add custom model input functionality in EditTagModal [`5f0322b6`](https://github.com/Veloera/Veloera/commit/5f0322b6)
- Update user group handling in Playground component [`379b08f6`](https://github.com/Veloera/Veloera/commit/379b08f6)
- Implement chat page state management in layout and sidebar [`afb7b661`](https://github.com/Veloera/Veloera/commit/afb7b661)
- Add renderModelPriceSimple function and update LogsTable component [`60710d6c`](https://github.com/Veloera/Veloera/commit/60710d6c)
- Update dependencies and restructure Playground component [`7cab9d7c`](https://github.com/Veloera/Veloera/commit/7cab9d7c)
- Enhance EditRedemption component with default name handling [`713de36e`](https://github.com/Veloera/Veloera/commit/713de36e)
- 首页优化 [`64e085dc`](https://github.com/Veloera/Veloera/commit/64e085dc)
- 侧边栏移动端优化 [`3622c664`](https://github.com/Veloera/Veloera/commit/3622c664)
- 优化playground搜索模型功能 [`18a8216a`](https://github.com/Veloera/Veloera/commit/18a8216a)
- update playground roleConfig [`2c79811c`](https://github.com/Veloera/Veloera/commit/2c79811c)
- support Azure Comm Service SMTP [`568d4e3f`](https://github.com/Veloera/Veloera/commit/568d4e3f)
- 兼容渠道搜索下标签聚合功能 [`aa82adc5`](https://github.com/Veloera/Veloera/commit/aa82adc5)
- add gemini tool_calls finish reason [`195ab1fd`](https://github.com/Veloera/Veloera/commit/195ab1fd)
- add deepseek channel type [`be556a23`](https://github.com/Veloera/Veloera/commit/be556a23)
- update go-epay [`98373f48`](https://github.com/Veloera/Veloera/commit/98373f48)
- support br [`4c809277`](https://github.com/Veloera/Veloera/commit/4c809277)
- support gzip [`3089af6b`](https://github.com/Veloera/Veloera/commit/3089af6b)
- add Cache-Control header to API requests [`de9a0d65`](https://github.com/Veloera/Veloera/commit/de9a0d65)
- add tag aggregation mode to channels API and UI [`88b0e6a7`](https://github.com/Veloera/Veloera/commit/88b0e6a7)
- 完善标签编辑（优先级，权重） [`6693072c`](https://github.com/Veloera/Veloera/commit/6693072c)
- 完善标签编辑 [`3053d941`](https://github.com/Veloera/Veloera/commit/3053d941)
- 完善标签编辑 [`9c4d3060`](https://github.com/Veloera/Veloera/commit/9c4d3060)
- add claude-3-5-haiku-20241022 [`999ba113`](https://github.com/Veloera/Veloera/commit/999ba113)
- support audio response_format #580 [`15842163`](https://github.com/Veloera/Veloera/commit/15842163)
- 暂时禁用透传功能 [`ed2ec695`](https://github.com/Veloera/Veloera/commit/ed2ec695)
- 暂时禁用透传功能 [`a167dd9a`](https://github.com/Veloera/Veloera/commit/a167dd9a)
- 一键编辑标签下渠道重定向 [`6e6e390f`](https://github.com/Veloera/Veloera/commit/6e6e390f)
- 渠道标签分组 [`0ce600ed`](https://github.com/Veloera/Veloera/commit/0ce600ed)
- add support for gemini-exp-1114 model / 添加 gemini-exp-1114 模型支持 [`46019329`](https://github.com/Veloera/Veloera/commit/46019329)
- 增加`GLOBAL_API_RATE_LIMIT_ENABLE`与`GLOBAL_WEB_RATE_LIMIT_ENABLE`环境变量，支持是否开启访问速率控制 [`08023f6d`](https://github.com/Veloera/Veloera/commit/08023f6d)
- 增加`GLOBAL_API_RATE_LIMIT_DURATION`与`GLOBAL_WEB_RATE_LIMIT_DURATION`环境变量，支持控制访问速率时间设置 [`fad29a8c`](https://github.com/Veloera/Veloera/commit/fad29a8c)
- 优化数据管理操作栏均为顶部 [`67d09d68`](https://github.com/Veloera/Veloera/commit/67d09d68)
- 优化switch组件的大小规格与整体表单一致 [`cdc02f66`](https://github.com/Veloera/Veloera/commit/cdc02f66)
- 统一运营设置页面的保存按钮大小规格 [`674abe5a`](https://github.com/Veloera/Veloera/commit/674abe5a)
- update LinuxDo icon [`66fa020b`](https://github.com/Veloera/Veloera/commit/66fa020b)
- playground用户分组设为默认选项 [`e291bb02`](https://github.com/Veloera/Veloera/commit/e291bb02)
- integrate Linux DO OAuth authentication [`046f859d`](https://github.com/Veloera/Veloera/commit/046f859d)
- realtime扣费时检测令牌额度 [`41311833`](https://github.com/Veloera/Veloera/commit/41311833)
- 完善audio倍率 [`3b53a2a5`](https://github.com/Veloera/Veloera/commit/3b53a2a5)
- 完善audio计费 [`97fdcd8e`](https://github.com/Veloera/Veloera/commit/97fdcd8e)
- update model ratio [`cbf0688b`](https://github.com/Veloera/Veloera/commit/cbf0688b)
- 美化日志页面 [`b40c2e10`](https://github.com/Veloera/Veloera/commit/b40c2e10)
- 日志详情完善 [`ee04dbd9`](https://github.com/Veloera/Veloera/commit/ee04dbd9)
- realtime pre consume [`e5c05d77`](https://github.com/Veloera/Veloera/commit/e5c05d77)
- realtime pre consume [`24b3ed50`](https://github.com/Veloera/Veloera/commit/24b3ed50)
- azure realtime [`8de79382`](https://github.com/Veloera/Veloera/commit/8de79382)
- realtime [`74f9006b`](https://github.com/Veloera/Veloera/commit/74f9006b)
- realtime [`33af069f`](https://github.com/Veloera/Veloera/commit/33af069f)
- 添加Mistral渠道 (close #546) [`4b48e490`](https://github.com/Veloera/Veloera/commit/4b48e490)
- support gpt-4o-audio-preview [`139a104b`](https://github.com/Veloera/Veloera/commit/139a104b)
- aws claude tools [`65e65097`](https://github.com/Veloera/Veloera/commit/65e65097)
- update claude models [`312ab448`](https://github.com/Veloera/Veloera/commit/312ab448)
- 上游渠道为OpenAI渠道类型时，透传请求 (close #532) [`8b676649`](https://github.com/Veloera/Veloera/commit/8b676649)
- 完善自定义聊天配置 [`d6359ec4`](https://github.com/Veloera/Veloera/commit/d6359ec4)
- 弃用旧的聊天配置 [`89ddf83b`](https://github.com/Veloera/Veloera/commit/89ddf83b)
- playground token name [`e298f2e5`](https://github.com/Veloera/Veloera/commit/e298f2e5)
- support embedding encoding_format param [`8cea6dff`](https://github.com/Veloera/Veloera/commit/8cea6dff)
- update aws claude [`5035cd05`](https://github.com/Veloera/Veloera/commit/5035cd05)
- update auto disable [`02c0c650`](https://github.com/Veloera/Veloera/commit/02c0c650)
- update model ratio [`f0b808a4`](https://github.com/Veloera/Veloera/commit/f0b808a4)
- update model ratio [`31d84ee3`](https://github.com/Veloera/Veloera/commit/31d84ee3)
- update model ratio [`9969ed2d`](https://github.com/Veloera/Veloera/commit/9969ed2d)
- 优化playground样式 [`04a68a85`](https://github.com/Veloera/Veloera/commit/04a68a85)
- Playground相关接口禁用AccessToken [`0cf53ac5`](https://github.com/Veloera/Veloera/commit/0cf53ac5)
- playground [`9a4ca1e2`](https://github.com/Veloera/Veloera/commit/9a4ca1e2)
- 更新令牌生成算法 [`0f95502b`](https://github.com/Veloera/Veloera/commit/0f95502b)
- 更新令牌生成算法 [`b58b1dc0`](https://github.com/Veloera/Veloera/commit/b58b1dc0)
- 不自动生成系统访问令牌 [`05d9aa61`](https://github.com/Veloera/Veloera/commit/05d9aa61)
- 添加.env配置文件和初始化环境变量 [`84f40b63`](https://github.com/Veloera/Veloera/commit/84f40b63)
- pricing page support multi groups #487 [`ed972eef`](https://github.com/Veloera/Veloera/commit/ed972eef)
- 无可选分组时关闭令牌分组功能 #485 [`c6ff785a`](https://github.com/Veloera/Veloera/commit/c6ff785a)
- update gemini flash completion ratio #479 [`af33f36c`](https://github.com/Veloera/Veloera/commit/af33f36c)
- update gemini completion ratio #479 [`3aa86a8c`](https://github.com/Veloera/Veloera/commit/3aa86a8c)
- 令牌分组 [`052bc207`](https://github.com/Veloera/Veloera/commit/052bc207)
- 添加令牌ip白名单功能 [`f505afdc`](https://github.com/Veloera/Veloera/commit/f505afdc)
- 优化界面显示 [`feb1d769`](https://github.com/Veloera/Veloera/commit/feb1d769)
- format o1 model max tokens param [`13c993d8`](https://github.com/Veloera/Veloera/commit/13c993d8)
- support o1 channel test [`cb738893`](https://github.com/Veloera/Veloera/commit/cb738893)
- support o1 channel test [`804aad3f`](https://github.com/Veloera/Veloera/commit/804aad3f)
- support OpenAI o1-preview and o1-mini [`3af62a3e`](https://github.com/Veloera/Veloera/commit/3af62a3e)
- support ollama multi-text embedding [`0cbf8e07`](https://github.com/Veloera/Veloera/commit/0cbf8e07)
- claude response return model name [`2650ec9b`](https://github.com/Veloera/Veloera/commit/2650ec9b)
- update chatgpt-4o token encoder [`e3b3fdec`](https://github.com/Veloera/Veloera/commit/e3b3fdec)
- remove lobe chat link #457 [`5863aa80`](https://github.com/Veloera/Veloera/commit/5863aa80)
- support jina embedding [`0830ef33`](https://github.com/Veloera/Veloera/commit/0830ef33)
- support more zhipu models [`97c18d0c`](https://github.com/Veloera/Veloera/commit/97c18d0c)
- support siliconflow embedding #447 [`4b1e83c4`](https://github.com/Veloera/Veloera/commit/4b1e83c4)
- 检测vertex渠道部署地区是否填写 [`01fd8b53`](https://github.com/Veloera/Veloera/commit/01fd8b53)
- 支持vertex ai渠道多个部署地区 [`e60f2001`](https://github.com/Veloera/Veloera/commit/e60f2001)
- support vertex ai #377 [`ac4262c5`](https://github.com/Veloera/Veloera/commit/ac4262c5)
- format claude messages when first role is not user [`a8ac8a25`](https://github.com/Veloera/Veloera/commit/a8ac8a25)
- rerank model mapping (close #444) [`144513f1`](https://github.com/Veloera/Veloera/commit/144513f1)
- support SiliconFlow (close #437, close #403) [`7c4d9d22`](https://github.com/Veloera/Veloera/commit/7c4d9d22)
- support gpt-4o-gizmo-* (close #436) [`d0f76a5c`](https://github.com/Veloera/Veloera/commit/d0f76a5c)
- update openai models list [`748e34fd`](https://github.com/Veloera/Veloera/commit/748e34fd)
- 避免暴露内部错误 [`f9392ca9`](https://github.com/Veloera/Veloera/commit/f9392ca9)
- update chatgpt-4o-latest model ratio [`1988c418`](https://github.com/Veloera/Veloera/commit/1988c418)
- update claude tools calling [`6cb0eb4b`](https://github.com/Veloera/Veloera/commit/6cb0eb4b)
- 区分额度不足和预扣费失败提示 [`4b5303a7`](https://github.com/Veloera/Veloera/commit/4b5303a7)
- 区分额度不足和预扣费失败提示 [`6eab0cc3`](https://github.com/Veloera/Veloera/commit/6eab0cc3)
- support gpt-4o-2024-08-06 [`93c6d765`](https://github.com/Veloera/Veloera/commit/93c6d765)
- log user id [`67878731`](https://github.com/Veloera/Veloera/commit/67878731)
- 优化充值订单号 [`88ba8a84`](https://github.com/Veloera/Veloera/commit/88ba8a84)
- 优化Gemini模型版本获取逻辑 [`e504665f`](https://github.com/Veloera/Veloera/commit/e504665f)
- 优化rpm查询 [`58b4c237`](https://github.com/Veloera/Veloera/commit/58b4c237)
- 优化日志查初始时间 [`54f6e660`](https://github.com/Veloera/Veloera/commit/54f6e660)
- 优化日志查询条件 [`3b1745c7`](https://github.com/Veloera/Veloera/commit/3b1745c7)
- 日志新增rpm和tpm数据。(close #384) [`c92ab3b5`](https://github.com/Veloera/Veloera/commit/c92ab3b5)
- ignore npm build dir [`fe16d51f`](https://github.com/Veloera/Veloera/commit/fe16d51f)
- support dify agent [`fbce3623`](https://github.com/Veloera/Veloera/commit/fbce3623)
- print user id when error [`b7bc205b`](https://github.com/Veloera/Veloera/commit/b7bc205b)
- support ollama tools [`88cc88c5`](https://github.com/Veloera/Veloera/commit/88cc88c5)
- print user id when error [`ab1d61d9`](https://github.com/Veloera/Veloera/commit/ab1d61d9)
- update log search [`12da7f64`](https://github.com/Veloera/Veloera/commit/12da7f64)
- update stream_options again [`9ef3212e`](https://github.com/Veloera/Veloera/commit/9ef3212e)
- update stream_options [`20da8228`](https://github.com/Veloera/Veloera/commit/20da8228)
- update stream_options [`436d08b4`](https://github.com/Veloera/Veloera/commit/436d08b4)
- support gpt-4o-mini image tokens [`e2cf6b1e`](https://github.com/Veloera/Veloera/commit/e2cf6b1e)
- update model ratio [`56afe47a`](https://github.com/Veloera/Veloera/commit/56afe47a)
- update model ratio [`67b74ada`](https://github.com/Veloera/Veloera/commit/67b74ada)
- support ali image [`c9100b21`](https://github.com/Veloera/Veloera/commit/c9100b21)
- support gemini tool calling (close #368) [`f96291a2`](https://github.com/Veloera/Veloera/commit/f96291a2)
- add UPDATE_TASK env [`14bf8650`](https://github.com/Veloera/Veloera/commit/14bf8650)
- 媒体请求计费选项 [`ae00a99c`](https://github.com/Veloera/Veloera/commit/ae00a99c)
- support claude tool calling [`11fd9935`](https://github.com/Veloera/Veloera/commit/11fd9935)
- support cloudflare audio [`ebb9b675`](https://github.com/Veloera/Veloera/commit/ebb9b675)
- update register page [`eb9b4b07`](https://github.com/Veloera/Veloera/commit/eb9b4b07)
- support cloudflare worker ai [`7b36a2b8`](https://github.com/Veloera/Veloera/commit/7b36a2b8)
- support claude stop_sequences [`c88f3741`](https://github.com/Veloera/Veloera/commit/c88f3741)
- update stream options [`0526c857`](https://github.com/Veloera/Veloera/commit/0526c857)
- update FORCE_STREAM_OPTION default value [`a984daa5`](https://github.com/Veloera/Veloera/commit/a984daa5)
- 允许设置是否检测mj任务已完成才可进行action操作 (close #349) [`03b130f2`](https://github.com/Veloera/Veloera/commit/03b130f2)
- able to use email to login (close #343,#348) [`45b9de9d`](https://github.com/Veloera/Veloera/commit/45b9de9d)
- 完善stream_options [`52debe75`](https://github.com/Veloera/Veloera/commit/52debe75)
- 完善stream_options [`df650273`](https://github.com/Veloera/Veloera/commit/df650273)
- support aws stream_options [`9896ba0a`](https://github.com/Veloera/Veloera/commit/9896ba0a)
- support claude stream_options [`e8b93ed6`](https://github.com/Veloera/Veloera/commit/e8b93ed6)
- support stream_options [`b0e234e8`](https://github.com/Veloera/Veloera/commit/b0e234e8)
- add env DIFY_DEBUG [`20d71711`](https://github.com/Veloera/Veloera/commit/20d71711)
- support jina rerank [`8a730cfe`](https://github.com/Veloera/Veloera/commit/8a730cfe)
- support cohere rerank [`8af4e28f`](https://github.com/Veloera/Veloera/commit/8af4e28f)
- support dify (close #299) [`e0ed59bf`](https://github.com/Veloera/Veloera/commit/e0ed59bf)
- 记录兑换时兑换码的ID (close #286) [`bd722211`](https://github.com/Veloera/Veloera/commit/bd722211)
- 记录渠道测试的消费日志 (close #334) [`cf3d8941`](https://github.com/Veloera/Veloera/commit/cf3d8941)
- 统计无限令牌的已用额度 (close #308) [`70110832`](https://github.com/Veloera/Veloera/commit/70110832)
- 统计无限令牌的已用额度 (close #308) [`752048df`](https://github.com/Veloera/Veloera/commit/752048df)
- update baidu [`eb382d28`](https://github.com/Veloera/Veloera/commit/eb382d28)
- log mj task id [`d306aea9`](https://github.com/Veloera/Veloera/commit/d306aea9)
- 完善日志扣费计算过程 [`584eefec`](https://github.com/Veloera/Veloera/commit/584eefec)
- support cohere first response time [`a7e3168c`](https://github.com/Veloera/Veloera/commit/a7e3168c)
- 支持设置流模式超时时间(gemini, claude) [`402a415c`](https://github.com/Veloera/Veloera/commit/402a415c)
- 支持设置流模式超时时间 [`fc6ae6bf`](https://github.com/Veloera/Veloera/commit/fc6ae6bf)
- support Spark4.0 Ultra [`d1778bb2`](https://github.com/Veloera/Veloera/commit/d1778bb2)
- first response time support aws [`11171122`](https://github.com/Veloera/Veloera/commit/11171122)
- first response time support gemini and claude [`f2654692`](https://github.com/Veloera/Veloera/commit/f2654692)
- 记录流模式首字时间 (close #323) [`79010dbf`](https://github.com/Veloera/Veloera/commit/79010dbf)
- only update task on master node [`cadd8aa6`](https://github.com/Veloera/Veloera/commit/cadd8aa6)
- auto ban 403 [`e3f66807`](https://github.com/Veloera/Veloera/commit/e3f66807)
- log channel status update time [`e8845ce1`](https://github.com/Veloera/Veloera/commit/e8845ce1)
- only update midjourney task on master node [`4eb6217b`](https://github.com/Veloera/Veloera/commit/4eb6217b)
- 记录自动禁用原因 (close #300) [`eb798805`](https://github.com/Veloera/Veloera/commit/eb798805)
- suno api 支持 [`c993ab27`](https://github.com/Veloera/Veloera/commit/c993ab27)
- update token encoder [`ecdcb379`](https://github.com/Veloera/Veloera/commit/ecdcb379)
- update tiktoken [`d2a0d9f7`](https://github.com/Veloera/Veloera/commit/d2a0d9f7)
- 支持设置worker访问请求中的图片地址 [`099068f5`](https://github.com/Veloera/Veloera/commit/099068f5)
- 添加自定义渠道提示 [`fa902cca`](https://github.com/Veloera/Veloera/commit/fa902cca)
- 添加自定义渠道提示 [`0c869681`](https://github.com/Veloera/Veloera/commit/0c869681)
- 增加重置模型倍率功能 (close #62) [`36fac2ba`](https://github.com/Veloera/Veloera/commit/36fac2ba)
- 完善获取模型列表功能 （close #237) [`0867d36f`](https://github.com/Veloera/Veloera/commit/0867d36f)
- update SettingsMagnification [`d6c1e3f3`](https://github.com/Veloera/Veloera/commit/d6c1e3f3)
- update model ratio [`774ce719`](https://github.com/Veloera/Veloera/commit/774ce719)
- update model ratio [`dbaa9390`](https://github.com/Veloera/Veloera/commit/dbaa9390)
- 自定义渠道功能变更 (#262) [`84da8850`](https://github.com/Veloera/Veloera/commit/84da8850)
- support minimax [`039fda91`](https://github.com/Veloera/Veloera/commit/039fda91)
- support minimax [`e0df8bbb`](https://github.com/Veloera/Veloera/commit/e0df8bbb)
- pre to delete custom channel type [`5e07ff85`](https://github.com/Veloera/Veloera/commit/5e07ff85)
- 日志显示重试信息 [`71dcf43c`](https://github.com/Veloera/Veloera/commit/71dcf43c)
- 完善模型价格获取逻辑 [`93858c32`](https://github.com/Veloera/Veloera/commit/93858c32)
- 完善模型价格页面 [`ff044de4`](https://github.com/Veloera/Veloera/commit/ff044de4)
- update model ratio [`eda3bd1c`](https://github.com/Veloera/Veloera/commit/eda3bd1c)
- update model ratio [`475dea96`](https://github.com/Veloera/Veloera/commit/475dea96)
- add pricing page [`5715fcf8`](https://github.com/Veloera/Veloera/commit/5715fcf8)
- dalle系列改为使用模型固定价格计费 [`71547849`](https://github.com/Veloera/Veloera/commit/71547849)
- 完善日志详情 [`39f6812a`](https://github.com/Veloera/Veloera/commit/39f6812a)
- 支持自定义特殊模型补全倍率 [`e8800415`](https://github.com/Veloera/Veloera/commit/e8800415)
- 只自动启用被自动禁用的渠道 (close #224) [`ecd06cf2`](https://github.com/Veloera/Veloera/commit/ecd06cf2)
- 填入相关模型 [`2dbf50dc`](https://github.com/Veloera/Veloera/commit/2dbf50dc)
- 编辑额度支持负数 [`d8c00604`](https://github.com/Veloera/Veloera/commit/d8c00604)
- 更方便地编辑用户额度 [`b427f027`](https://github.com/Veloera/Veloera/commit/b427f027)
- 日志详情展示模型价格 [`6fb1fbfe`](https://github.com/Veloera/Veloera/commit/6fb1fbfe)
- 限制邮箱别名 [`4641d446`](https://github.com/Veloera/Veloera/commit/4641d446)
- update model ratio [`d7a343e2`](https://github.com/Veloera/Veloera/commit/d7a343e2)
- log completionRatio [`16b9aacb`](https://github.com/Veloera/Veloera/commit/16b9aacb)
- able to set AccountFilter [`cad380eb`](https://github.com/Veloera/Veloera/commit/cad380eb)
- update midjourney task info update timeout [`234e39dd`](https://github.com/Veloera/Veloera/commit/234e39dd)
- safe send channel [`21f32605`](https://github.com/Veloera/Veloera/commit/21f32605)
- log status code [`be4809b9`](https://github.com/Veloera/Veloera/commit/be4809b9)
- claude 整理prompt [`a14fa1ad`](https://github.com/Veloera/Veloera/commit/a14fa1ad)
- update model ratio [`20aaf307`](https://github.com/Veloera/Veloera/commit/20aaf307)
- support cohere (close #195) [`bfcaccc2`](https://github.com/Veloera/Veloera/commit/bfcaccc2)
- dalle系列日志记录更多信息 [`3f448ba4`](https://github.com/Veloera/Veloera/commit/3f448ba4)
- 完善函数计费 [`********`](https://github.com/Veloera/Veloera/commit/********)
- update shouldRetry [`89ebd855`](https://github.com/Veloera/Veloera/commit/89ebd855)
- 自动整理claude不规范prompt [`1a39ef74`](https://github.com/Veloera/Veloera/commit/1a39ef74)
- support aws claude [`92941276`](https://github.com/Veloera/Veloera/commit/92941276)
- 支持ollama embedding数组传参 [`6b97842f`](https://github.com/Veloera/Veloera/commit/6b97842f)
- 启用函数计费 [`bdc65bdb`](https://github.com/Veloera/Veloera/commit/bdc65bdb)
- update gemini model [`76dc7af8`](https://github.com/Veloera/Veloera/commit/76dc7af8)
- 登陆美化 [`892b7d1a`](https://github.com/Veloera/Veloera/commit/892b7d1a)
- 状态码复写 [`6b71db7c`](https://github.com/Veloera/Veloera/commit/6b71db7c)
- 在重试时打印重试信息 [`b8fb351f`](https://github.com/Veloera/Veloera/commit/b8fb351f)
- update cache [`e6765ef3`](https://github.com/Veloera/Veloera/commit/e6765ef3)
- update cache [`4ef98ba7`](https://github.com/Veloera/Veloera/commit/4ef98ba7)
- update cache #204 [`65b85377`](https://github.com/Veloera/Veloera/commit/65b85377)
- 完善数据看板 #190 [`c6e85d5b`](https://github.com/Veloera/Veloera/commit/c6e85d5b)
- 可设置是否转发上游mj图片地址 [`1162683b`](https://github.com/Veloera/Veloera/commit/1162683b)
- 前端不显示敏感信息 [`818bd824`](https://github.com/Veloera/Veloera/commit/818bd824)
- support google v1beta and Gemini Ultra [`21250a46`](https://github.com/Veloera/Veloera/commit/21250a46)
- support gpt-4-turbo [`f1881476`](https://github.com/Veloera/Veloera/commit/f1881476)
- automatically ban channels that exceeded quota [`a7cfce24`](https://github.com/Veloera/Veloera/commit/a7cfce24)
- 支持未开启缓存下本地重试 [`462c328d`](https://github.com/Veloera/Veloera/commit/462c328d)
- 超时状态码不重试 [`fed1a1d6`](https://github.com/Veloera/Veloera/commit/fed1a1d6)
- 钱包兼容非货币形式显示额度 [`1cd1e54b`](https://github.com/Veloera/Veloera/commit/1cd1e54b)
- 钱包兼容非货币形式显示额度 [`3db64afc`](https://github.com/Veloera/Veloera/commit/3db64afc)
- 钱包兼容非货币形式显示额度 [`bc9cfa5d`](https://github.com/Veloera/Veloera/commit/bc9cfa5d)
- able to set default test model (#138) [`660b9b3c`](https://github.com/Veloera/Veloera/commit/660b9b3c)
- 本地重试 [`4b60528c`](https://github.com/Veloera/Veloera/commit/4b60528c)
- add Claude TopK [`278fd391`](https://github.com/Veloera/Veloera/commit/278fd391)
- 支持 /mj-{mode} 路径 [`49df4b6e`](https://github.com/Veloera/Veloera/commit/49df4b6e)
- able to set smtp ssl [`5c39f540`](https://github.com/Veloera/Veloera/commit/5c39f540)
- 开启redis的情况下设置SYNC_FREQUENCY默认为60 [`786ccc7d`](https://github.com/Veloera/Veloera/commit/786ccc7d)
- support ollama embedding [`8eedad94`](https://github.com/Veloera/Veloera/commit/8eedad94)
- 进一步防止暴露数上游以及数据库地址 [`d8c91fa4`](https://github.com/Veloera/Veloera/commit/d8c91fa4)
- support gemini-1.5 [`1587ea56`](https://github.com/Veloera/Veloera/commit/1587ea56)
- remove azure model TrimPrefix [`a7a1fc61`](https://github.com/Veloera/Veloera/commit/a7a1fc61)
- prettier [`15e73073`](https://github.com/Veloera/Veloera/commit/15e73073)
- 从本地读取字体 (close #130) [`525fc1b3`](https://github.com/Veloera/Veloera/commit/525fc1b3)
- 首页加载速度优化 [`58f2cf3a`](https://github.com/Veloera/Veloera/commit/58f2cf3a)
- vite [`93be61aa`](https://github.com/Veloera/Veloera/commit/93be61aa)
- 统一错误提示 [`a232afe9`](https://github.com/Veloera/Veloera/commit/a232afe9)
- 保留功能 [`2db42826`](https://github.com/Veloera/Veloera/commit/2db42826)
- 初步兼容生成内容检查 [`64b9d3b5`](https://github.com/Veloera/Veloera/commit/64b9d3b5)
- 初步兼容敏感词过滤 [`7a663d26`](https://github.com/Veloera/Veloera/commit/7a663d26)
- support perplexity [`beadb98a`](https://github.com/Veloera/Veloera/commit/beadb98a)
- support perplexity [`578b5f65`](https://github.com/Veloera/Veloera/commit/578b5f65)
- midjourneys表添加索引 [`f82aa956`](https://github.com/Veloera/Veloera/commit/f82aa956)
- print midjourney-proxy request error [`4b952b85`](https://github.com/Veloera/Veloera/commit/4b952b85)
- 添加回调未开启提示 [`a6ba1d01`](https://github.com/Veloera/Veloera/commit/a6ba1d01)
- Add logs page-size [`25ec9991`](https://github.com/Veloera/Veloera/commit/25ec9991)
- Only update weight and priority when blur [`7f22d585`](https://github.com/Veloera/Veloera/commit/7f22d585)
- Remember and automatically set page-size (close #118) [`dfdeadf1`](https://github.com/Veloera/Veloera/commit/dfdeadf1)
- 允许开关mj回调 [`4917e5a9`](https://github.com/Veloera/Veloera/commit/4917e5a9)
- support InsightFace (close #60) [`9b5353a8`](https://github.com/Veloera/Veloera/commit/9b5353a8)
- support image-seed (close #86) [`bc5a54df`](https://github.com/Veloera/Veloera/commit/bc5a54df)
- 兼容自定义变焦，完善modal操作 [`d704902b`](https://github.com/Veloera/Veloera/commit/d704902b)
- 超过一小时的任务自动失败 [`614220a0`](https://github.com/Veloera/Veloera/commit/614220a0)
- support Anthropic Claude 3.0 Haiku (close #116) [`98c1f66d`](https://github.com/Veloera/Veloera/commit/98c1f66d)
- 将操作拆分成单独的模型 [`3d10c9f0`](https://github.com/Veloera/Veloera/commit/3d10c9f0)
- 操作细分 [`d5ffaf25`](https://github.com/Veloera/Veloera/commit/d5ffaf25)
- support shorten [`2ad59141`](https://github.com/Veloera/Veloera/commit/2ad59141)
- 兼容变焦功能 [`728dbed2`](https://github.com/Veloera/Veloera/commit/728dbed2)
- 请求超时处理 [`fd3a41ba`](https://github.com/Veloera/Veloera/commit/fd3a41ba)
- 初步兼容midjourney-proxy-plus [`37c0c8eb`](https://github.com/Veloera/Veloera/commit/37c0c8eb)
- add parameter top_k [`c0123064`](https://github.com/Veloera/Veloera/commit/c0123064)
- support ollama (close #112) [`d53d3386`](https://github.com/Veloera/Veloera/commit/d53d3386)
- 前端美化 [`c29680b3`](https://github.com/Veloera/Veloera/commit/c29680b3)
- update claude default model ratio [`655dfe0d`](https://github.com/Veloera/Veloera/commit/655dfe0d)
- support Claude 3 [`4a0af1ea`](https://github.com/Veloera/Veloera/commit/4a0af1ea)
- support claude3 not stream [`c2965eb8`](https://github.com/Veloera/Veloera/commit/c2965eb8)
- 添加充值记录按钮 [`280c63e1`](https://github.com/Veloera/Veloera/commit/280c63e1)
- 支持部分渠道的system角色 (close #89) [`3ab4f145`](https://github.com/Veloera/Veloera/commit/3ab4f145)
- add "/api/status/test" [`fe7f42fc`](https://github.com/Veloera/Veloera/commit/fe7f42fc)
- 记录请求的token用量 [`912f46fc`](https://github.com/Veloera/Veloera/commit/912f46fc)
- 解决渠道密钥长度限制问题 [`1ad11123`](https://github.com/Veloera/Veloera/commit/1ad11123)
- update SparkDesk model ratio [`a16e9493`](https://github.com/Veloera/Veloera/commit/a16e9493)
- 可自定义支付回调地址及最低购买数量 [`72194bda`](https://github.com/Veloera/Veloera/commit/72194bda)
- 数据看板颜色统一 [`d6d8ad2b`](https://github.com/Veloera/Veloera/commit/d6d8ad2b)
- telegram login and bind [`84144306`](https://github.com/Veloera/Veloera/commit/84144306)
- 可设置默认折叠侧边栏 [`a8f0c5da`](https://github.com/Veloera/Veloera/commit/a8f0c5da)
- 修复智谱GLM-4V流模式异常 [`84cac72a`](https://github.com/Veloera/Veloera/commit/84cac72a)
- Add channel search by model field (close #72) [`413d4f0a`](https://github.com/Veloera/Veloera/commit/413d4f0a)
- "/v1/models" 只返回用户可用模型 (close #78) [`feb40db2`](https://github.com/Veloera/Veloera/commit/feb40db2)
- 支持智谱GLM-4V [`b4645d10`](https://github.com/Veloera/Veloera/commit/b4645d10)
- 初步重构完成 [`6013219f`](https://github.com/Veloera/Veloera/commit/6013219f)
- 初步重构 [`5b18cd6b`](https://github.com/Veloera/Veloera/commit/5b18cd6b)
- 未配置在线支付时，限制用户输入金额 (close #74) [`608c945a`](https://github.com/Veloera/Veloera/commit/608c945a)
- 在线支付限制输入金额 [`05beade3`](https://github.com/Veloera/Veloera/commit/05beade3)
- 记录更多的错误信息 [`81167c43`](https://github.com/Veloera/Veloera/commit/81167c43)
- support logprobs [`6fa1837c`](https://github.com/Veloera/Veloera/commit/6fa1837c)
- 优化令牌无效提示 [`ba0c5bb4`](https://github.com/Veloera/Veloera/commit/ba0c5bb4)
- add model gpt-3.5-turbo-0125 [`a0c4e9f1`](https://github.com/Veloera/Veloera/commit/a0c4e9f1)
- Happy New Year [`d6d91e43`](https://github.com/Veloera/Veloera/commit/d6d91e43)
- 添加成功时自动启用通道功能, close #27 [`5c8f8b49`](https://github.com/Veloera/Veloera/commit/5c8f8b49)
- 支持测试渠道时自选模型 [`affe5111`](https://github.com/Veloera/Veloera/commit/affe5111)
- add model text-embedding-3 [`364d4f96`](https://github.com/Veloera/Veloera/commit/364d4f96)
- add model gpt-4-turbo-preview [`bce8a3e2`](https://github.com/Veloera/Veloera/commit/bce8a3e2)
- update model-ratio [`3dc5d4d9`](https://github.com/Veloera/Veloera/commit/3dc5d4d9)
- token缓存逻辑更新（实验性） [`ac3e2785`](https://github.com/Veloera/Veloera/commit/ac3e2785)
- 数据看板加入每列总计 [`e8188902`](https://github.com/Veloera/Veloera/commit/e8188902)
- 优化vision计费逻辑 [`1ee8edcf`](https://github.com/Veloera/Veloera/commit/1ee8edcf)
- add midjourney price log [`a3921ea5`](https://github.com/Veloera/Veloera/commit/a3921ea5)
- 请求出现 0 token的时候，加入错误提示并打印日志 [`b3f46223`](https://github.com/Veloera/Veloera/commit/b3f46223)
- 令牌聊天新增ChatGPT Web & Midjourney支持 [`c59a33e8`](https://github.com/Veloera/Veloera/commit/c59a33e8)
- 美化日志详情 [`9bcd24fc`](https://github.com/Veloera/Veloera/commit/9bcd24fc)
- 日志优化逻辑，新增请求时间和是否为stream字段 [`cbdce181`](https://github.com/Veloera/Veloera/commit/cbdce181)
- able to change gemini safety setting [`aca8d253`](https://github.com/Veloera/Veloera/commit/aca8d253)
- able to fix channels [`6a24e895`](https://github.com/Veloera/Veloera/commit/6a24e895)
- support Azure dall-e [`75b6327f`](https://github.com/Veloera/Veloera/commit/75b6327f)
- 可设置令牌能调用的模型 [`1244963e`](https://github.com/Veloera/Veloera/commit/1244963e)
- 完善数据看板选择时间区间 [`1c2bba89`](https://github.com/Veloera/Veloera/commit/1c2bba89)
- 新增数据看板 [`bf8794d2`](https://github.com/Veloera/Veloera/commit/bf8794d2)
- 允许关闭绘图选项 [`c09df83f`](https://github.com/Veloera/Veloera/commit/c09df83f)
- add support for davinci-002 and babbage-002 [`afbd5850`](https://github.com/Veloera/Veloera/commit/afbd5850)
- 加入渠道加权随机功能 [`bdd611fd`](https://github.com/Veloera/Veloera/commit/bdd611fd)
- support xunfei v3 (#637) [`aec343dc`](https://github.com/Veloera/Veloera/commit/aec343dc)
- able to set RELAY_TIMEOUT [`89d458b9`](https://github.com/Veloera/Veloera/commit/89d458b9)
- support ERNIE-Bot-4 (close #608) [`63fafba1`](https://github.com/Veloera/Veloera/commit/63fafba1)
- add cloudflare ai gateway support for image & audio (#607) [`3b483639`](https://github.com/Veloera/Veloera/commit/3b483639)
- able to delete all disabled channels [`82444424`](https://github.com/Veloera/Veloera/commit/82444424)
- support cloudflare AI gateway now (close #565, #598) [`e28d4b17`](https://github.com/Veloera/Veloera/commit/e28d4b17)
- 新增充值分组倍率设置 [`a4e26926`](https://github.com/Veloera/Veloera/commit/a4e26926)
- 充值前弹窗确认 [`bd22fee1`](https://github.com/Veloera/Veloera/commit/bd22fee1)
- support Tencent's model (close #519) [`b4b4acc2`](https://github.com/Veloera/Veloera/commit/b4b4acc2)
- able to delete all manually disabled channels (close #539) [`8d34b7a7`](https://github.com/Veloera/Veloera/commit/8d34b7a7)
- 优先优先级排序渠道 [`d429992b`](https://github.com/Veloera/Veloera/commit/d429992b)
- 优先用优先级排序渠道 [`1bb65fa0`](https://github.com/Veloera/Veloera/commit/1bb65fa0)
- 防止渠道地址传到用户端 [`929cd79e`](https://github.com/Veloera/Veloera/commit/929cd79e)
- 渠道新可选是否自动禁用功能 [`b06c370f`](https://github.com/Veloera/Veloera/commit/b06c370f)
- 渠道新可选是否自动禁用功能 [`80271b33`](https://github.com/Veloera/Veloera/commit/80271b33)
- add support for gpt-3.5-turbo-instruct (close #545) [`f5a1cd34`](https://github.com/Veloera/Veloera/commit/f5a1cd34)
- 新增渠道页面选择每页显示数量 [`3595e352`](https://github.com/Veloera/Veloera/commit/3595e352)
- supprt channel priority now & record channel id in log (#484) [`ecf8a6d8`](https://github.com/Veloera/Veloera/commit/ecf8a6d8)
- support non-stream mode for xunfei (#498) [`24df3e5f`](https://github.com/Veloera/Veloera/commit/24df3e5f)
- able to delete logs now (close #486) [`328aa682`](https://github.com/Veloera/Veloera/commit/328aa682)
- create new log file when too many logs recorded [`4335f005`](https://github.com/Veloera/Veloera/commit/4335f005)
- 渠道支持指定组织 [`cc09dd4f`](https://github.com/Veloera/Veloera/commit/cc09dd4f)
- 避免请求出错时暴露渠道信息 [`d1d78961`](https://github.com/Veloera/Veloera/commit/d1d78961)
- 改为转发的方式获取midjourney图片 [`d9deda18`](https://github.com/Veloera/Veloera/commit/d9deda18)
- 改为转发的方式获取midjourney图片 [`84645128`](https://github.com/Veloera/Veloera/commit/84645128)
- add midjourney log [`4895bd74`](https://github.com/Veloera/Veloera/commit/4895bd74)
- 删除无用功能 [`4a836a4c`](https://github.com/Veloera/Veloera/commit/4a836a4c)
- able to config rate limit (close #477) [`bd6fe1e9`](https://github.com/Veloera/Veloera/commit/bd6fe1e9)
- add channel type FastGPT [`7e575abb`](https://github.com/Veloera/Veloera/commit/7e575abb)
- add batch update support (close #414) [`c3dc315e`](https://github.com/Veloera/Veloera/commit/c3dc315e)
- support aiproxy's library [`04acdb1c`](https://github.com/Veloera/Veloera/commit/04acdb1c)
- supper OpenRouter now (close #333, close #340) [`56b50073`](https://github.com/Veloera/Veloera/commit/56b50073)
- supper whisper now (close #197) [`d09d3174`](https://github.com/Veloera/Veloera/commit/d09d3174)
- support 360's models (close #331, close #461) [`5ee24e8a`](https://github.com/Veloera/Veloera/commit/5ee24e8a)
- support xunfei's v2 api (#442, close #440) [`7e058bfb`](https://github.com/Veloera/Veloera/commit/7e058bfb)
- show total quota consumption only when user click (#448) [`1b56becf`](https://github.com/Veloera/Veloera/commit/1b56becf)
- able to refresh baidu access token automatically (#400, close #401) [`af20063a`](https://github.com/Veloera/Veloera/commit/af20063a)
- support PostgreSQL now [`e0b4f96b`](https://github.com/Veloera/Veloera/commit/e0b4f96b)
- improve frontend (#387) [`c58f7102`](https://github.com/Veloera/Veloera/commit/c58f7102)
- add chat button for each token (#363) [`5a62357c`](https://github.com/Veloera/Veloera/commit/5a62357c)
- able to copy scheme of ama, opencat & chatgpt next web (#343) [`1dfa190e`](https://github.com/Veloera/Veloera/commit/1dfa190e)
- support email domain whitelist (#337) [`3fca6ff5`](https://github.com/Veloera/Veloera/commit/3fca6ff5)
- support xunfei's llm (close #206) [`8a866078`](https://github.com/Veloera/Veloera/commit/8a866078)
- support baidu's embedding model (close #324) [`130e6bfd`](https://github.com/Veloera/Veloera/commit/130e6bfd)
- support ali's llm (close #326) [`e92da792`](https://github.com/Veloera/Veloera/commit/e92da792)
- add access_until field for subscription api (#295) [`dccd66b8`](https://github.com/Veloera/Veloera/commit/dccd66b8)
- able to delete account by self (#294) [`2fcd6852`](https://github.com/Veloera/Veloera/commit/2fcd6852)
- support zhipu's ChatGLM (close #289) [`26c6719e`](https://github.com/Veloera/Veloera/commit/26c6719e)
- support Google PaLM2 (close #105) [`8f721d67`](https://github.com/Veloera/Veloera/commit/8f721d67)
- support baidu's models now (close #286) [`9a1db616`](https://github.com/Veloera/Veloera/commit/9a1db616)
- support claude now (close #150) [`2ff15baf`](https://github.com/Veloera/Veloera/commit/2ff15baf)
- retry on failed (close #112) [`35cfebee`](https://github.com/Veloera/Veloera/commit/35cfebee)
- support ChatGLM2 (close #274) [`0e088f7c`](https://github.com/Veloera/Veloera/commit/0e088f7c)
- support custom model now (close #276) [`ccf7709e`](https://github.com/Veloera/Veloera/commit/ccf7709e)
- add turnstile for login form (#263) [`d592e2c8`](https://github.com/Veloera/Veloera/commit/d592e2c8)
- initial support of Dall-E (#148, #266) [`b520b546`](https://github.com/Veloera/Veloera/commit/b520b546)
- add support for /v1/engines/text-embedding-ada-002/embeddings (#224, close #222) [`81c59011`](https://github.com/Veloera/Veloera/commit/81c59011)
- disable channel when account_deactivated received (close #271) [`abc53cb2`](https://github.com/Veloera/Veloera/commit/abc53cb2)
- able to approximate token (close #207) [`701aaba1`](https://github.com/Veloera/Veloera/commit/701aaba1)
- support balance query for CloseAI (#240) [`d383302e`](https://github.com/Veloera/Veloera/commit/d383302e)
- support channel type AIGC2D (#220) [`b1b3651e`](https://github.com/Veloera/Veloera/commit/b1b3651e)
- support model remap now [`0941e294`](https://github.com/Veloera/Veloera/commit/0941e294)
- support /v1/edits now (close #196) [`9b178a28`](https://github.com/Veloera/Veloera/commit/9b178a28)
- able to query token with admin user [`3ce982d8`](https://github.com/Veloera/Veloera/commit/3ce982d8)
- able to query logs now (close #144) [`cccf5e4a`](https://github.com/Veloera/Veloera/commit/cccf5e4a)
- add English support [`03c05bdb`](https://github.com/Veloera/Veloera/commit/03c05bdb)
- show used quota in token table [`9ac5410d`](https://github.com/Veloera/Veloera/commit/9ac5410d)
- able to display token billing stat via billing api (close #186) [`7edc2b53`](https://github.com/Veloera/Veloera/commit/7edc2b53)
- support automatic channel testing & balance updates (close #11, close #59) [`4463224f`](https://github.com/Veloera/Veloera/commit/4463224f)
- support search channels by key (close #185) [`ad1049b0`](https://github.com/Veloera/Veloera/commit/ad1049b0)
- do not access database before response return (close #158) [`6d961064`](https://github.com/Veloera/Veloera/commit/6d961064)
- select channel without database (#158) [`ba54c719`](https://github.com/Veloera/Veloera/commit/ba54c719)
- support custom base url for channels [`e398f470`](https://github.com/Veloera/Veloera/commit/e398f470)
- able to display quota in dollar [`b179c2f2`](https://github.com/Veloera/Veloera/commit/b179c2f2)
- use cache to avoid database access (#158) [`3d76a974`](https://github.com/Veloera/Veloera/commit/3d76a974)
- able to add chat page link now (close #70) [`d9764037`](https://github.com/Veloera/Veloera/commit/d9764037)
- support aff now (close #75) [`c5837c3b`](https://github.com/Veloera/Veloera/commit/c5837c3b)
- support API2GPT platform (#173) [`a43b1e2a`](https://github.com/Veloera/Veloera/commit/a43b1e2a)
- add token name to log (#172) [`46c43396`](https://github.com/Veloera/Veloera/commit/46c43396)
- support update AIProxy balance (#171) [`b7d71b4f`](https://github.com/Veloera/Veloera/commit/b7d71b4f)
- return a not found response if requested a wrong API endpoints [`70ed126c`](https://github.com/Veloera/Veloera/commit/70ed126c)
- record channel's used quota (close #137) [`0cdab80a`](https://github.com/Veloera/Veloera/commit/0cdab80a)
- record used quota & request count (close #102, #165) [`760183a9`](https://github.com/Veloera/Veloera/commit/760183a9)
- able to disable quota consumption recording (close #156) [`593e1926`](https://github.com/Veloera/Veloera/commit/593e1926)
- now one channel can belong to multiple groups (close #153) [`7f9577a3`](https://github.com/Veloera/Veloera/commit/7f9577a3)
- add new released models [`323f3d26`](https://github.com/Veloera/Veloera/commit/323f3d26)
- add support for updating balance of channel typpe OpenAI-SB (#146, close #125) [`47ca449e`](https://github.com/Veloera/Veloera/commit/47ca449e)
- able to set group ratio now (close #62, close #142) [`596446db`](https://github.com/Veloera/Veloera/commit/596446db)
- support /v1/moderations now (close #117) [`4339f45f`](https://github.com/Veloera/Veloera/commit/4339f45f)
- now able to check all user's log [`64db3932`](https://github.com/Veloera/Veloera/commit/64db3932)
- now user can check its topup & consume history (close #78, close #95) [`74f508e8`](https://github.com/Veloera/Veloera/commit/74f508e8)
- support set proxy for channel OpenAI (close #139) [`8901f038`](https://github.com/Veloera/Veloera/commit/8901f038)
- able to check topup history & consumption history (#78, #95) [`45e9fd66`](https://github.com/Veloera/Veloera/commit/45e9fd66)
- support /v1/completions (close #115) [`4b6adaec`](https://github.com/Veloera/Veloera/commit/4b6adaec)
- able to manage group now [`2c53424d`](https://github.com/Veloera/Veloera/commit/2c53424d)
- support group now (close #17, close #72, close #85, close #104, close #136) [`2ad22e14`](https://github.com/Veloera/Veloera/commit/2ad22e14)
- the format of key is now constant with that of OpenAI [`2847a088`](https://github.com/Veloera/Veloera/commit/2847a088)
- return user's quota with billing api (close #92) [`d4794fc0`](https://github.com/Veloera/Veloera/commit/d4794fc0)
- support channel remain quota query (close #79) [`171b8185`](https://github.com/Veloera/Veloera/commit/171b8185)
- PaLM support is WIP (#105) [`bcca0cc0`](https://github.com/Veloera/Veloera/commit/bcca0cc0)
- able to manage user's quota now [`61e682ca`](https://github.com/Veloera/Veloera/commit/61e682ca)
- support channel AI Proxy now [`cfd58711`](https://github.com/Veloera/Veloera/commit/cfd58711)
- support channel ai.ls now (close #99) [`3711f4a7`](https://github.com/Veloera/Veloera/commit/3711f4a7)
- now slave server can sync options with master server (close #88) [`2d39a135`](https://github.com/Veloera/Veloera/commit/2d39a135)
- support redirecting frontend url now (close #89) [`3c6834a7`](https://github.com/Veloera/Veloera/commit/3c6834a7)
- support dummy sk- prefix for token (#82) [`73d5e0f2`](https://github.com/Veloera/Veloera/commit/73d5e0f2)
- API `/models` & `/models/:model` implemented (close #68) [`efc744ca`](https://github.com/Veloera/Veloera/commit/efc744ca)
- support max_tokens now (#52) [`c9ac5e39`](https://github.com/Veloera/Veloera/commit/c9ac5e39)
- disable operations for root user (close #76) [`69cf1de7`](https://github.com/Veloera/Veloera/commit/69cf1de7)
- able to set pre consumed quota now [`4d6172a2`](https://github.com/Veloera/Veloera/commit/4d6172a2)
- add database migration script [`6d1e5cb5`](https://github.com/Veloera/Veloera/commit/6d1e5cb5)
- show users' remaining quota in user management page (close #46) [`7c56a36a`](https://github.com/Veloera/Veloera/commit/7c56a36a)
- automatically disable channel when error occurred (#59) [`a1f61384`](https://github.com/Veloera/Veloera/commit/a1f61384)
- add refresh button [`44ebae15`](https://github.com/Veloera/Veloera/commit/44ebae15)
- able to test all enabled channels (#59) [`d267211e`](https://github.com/Veloera/Veloera/commit/d267211e)
- save response time & test time (#59) [`225176aa`](https://github.com/Veloera/Veloera/commit/225176aa)
- able to test channels now (#59) [`443a22b7`](https://github.com/Veloera/Veloera/commit/443a22b7)
- double check before deletion [`b44f0519`](https://github.com/Veloera/Veloera/commit/b44f0519)
- able to customize system name & logo now [`926951ee`](https://github.com/Veloera/Veloera/commit/926951ee)
- able to use any link as about page (#60) [`2cdc718f`](https://github.com/Veloera/Veloera/commit/2cdc718f)
- able to customize home page with link (close #60) [`224bebe6`](https://github.com/Veloera/Veloera/commit/224bebe6)
- able to customize home page now (#24) [`d84c2f5c`](https://github.com/Veloera/Veloera/commit/d84c2f5c)
- able to configure smtp from now (close #34) [`f5f4e6fb`](https://github.com/Veloera/Veloera/commit/f5f4e6fb)
- support batch creation of channels (close #58) [`dc4a6cb7`](https://github.com/Veloera/Veloera/commit/dc4a6cb7)
- support specific default api version now (#57) [`83e86b9f`](https://github.com/Veloera/Veloera/commit/83e86b9f)
- Azure API supported without verification (#48, #57) [`7a3378b4`](https://github.com/Veloera/Veloera/commit/7a3378b4)
- now supports custom smtp port [`5c694a15`](https://github.com/Veloera/Veloera/commit/5c694a15)
- support /v1/embeddings now (close #50) [`6e1ef750`](https://github.com/Veloera/Veloera/commit/6e1ef750)
- able to configure ratio for more models now (close #53) [`d9db16e9`](https://github.com/Veloera/Veloera/commit/d9db16e9)
- add Docker Compose support (#55) [`80065de8`](https://github.com/Veloera/Veloera/commit/80065de8)
- double check before deleting a user [`16f53b5a`](https://github.com/Veloera/Veloera/commit/16f53b5a)
- support API `/dashboard/billing/credit_grants` now (#45) [`3071300c`](https://github.com/Veloera/Veloera/commit/3071300c)
- able to configure ratio for different models (close #26) [`d93cb8f6`](https://github.com/Veloera/Veloera/commit/d93cb8f6)
- support configuring ratio when estimating token number in stream mode [`480e789c`](https://github.com/Veloera/Veloera/commit/480e789c)
- now use token as the unit of quota (close #33) [`053bb85a`](https://github.com/Veloera/Veloera/commit/053bb85a)
- able to set top up link now [`f97c2b4c`](https://github.com/Veloera/Veloera/commit/f97c2b4c)
- able to set initial quota for new user (close #22) [`b9cc5dfa`](https://github.com/Veloera/Veloera/commit/b9cc5dfa)
- able to manage system vai access token (close #12) [`8c305dc1`](https://github.com/Veloera/Veloera/commit/8c305dc1)
- download redemption codes as file (#12) [`f62a671f`](https://github.com/Veloera/Veloera/commit/f62a671f)
- now user can top up via redemption code (close #9) [`9e2f2383`](https://github.com/Veloera/Veloera/commit/9e2f2383)
- redirect to login page if login expired (close #18) [`f16a2a56`](https://github.com/Veloera/Veloera/commit/f16a2a56)
- limit the ability of common user to set the remaining usage times of token (#9) [`03491029`](https://github.com/Veloera/Veloera/commit/03491029)
- able to relay dashboard api now [`05dd7dfd`](https://github.com/Veloera/Veloera/commit/05dd7dfd)
- enable gzip on api route & web route [`284beed8`](https://github.com/Veloera/Veloera/commit/284beed8)
- relay all kinds of requests [`46dfc6dc`](https://github.com/Veloera/Veloera/commit/46dfc6dc)
- improve the token edit page [`5ed4a3d4`](https://github.com/Veloera/Veloera/commit/5ed4a3d4)
- able to set the token's expiration time and number of uses [`918ba608`](https://github.com/Veloera/Veloera/commit/918ba608)

### Bug Fixes
- Include prefixed model names in user model list [`e3c416b1`](https://github.com/Veloera/Veloera/commit/e3c416b1)
- Fix username confilct [`62142698`](https://github.com/Veloera/Veloera/commit/62142698)
- No longer accept multi-key in Vertex channel [`fdd609c3`](https://github.com/Veloera/Veloera/commit/fdd609c3)
- Cannot save check in settings [`8996d68a`](https://github.com/Veloera/Veloera/commit/8996d68a)
- Fix missing VERSION file which is required by Dockerfile [`f93e2f94`](https://github.com/Veloera/Veloera/commit/f93e2f94)
- Fixed display error when success redeem a code [`4099b331`](https://github.com/Veloera/Veloera/commit/4099b331)
- Fix sometimes max_uses will be string [`b3f7bdbf`](https://github.com/Veloera/Veloera/commit/b3f7bdbf)
- Fix a little typo in EditUser [`7bcec0d2`](https://github.com/Veloera/Veloera/commit/7bcec0d2)
- Typo in docker-compose.yml [`a27c86a7`](https://github.com/Veloera/Veloera/commit/a27c86a7)
- claude parallel function calling [`214ca4db`](https://github.com/Veloera/Veloera/commit/214ca4db)
- try to fix claude to openai format mcp #966 [`99efc1fb`](https://github.com/Veloera/Veloera/commit/99efc1fb)
- wrong systemStr for Claude (OpenAI Upstream) [`eee6dee5`](https://github.com/Veloera/Veloera/commit/eee6dee5)
- update model name handling in UI and localization [`dcf78787`](https://github.com/Veloera/Veloera/commit/dcf78787)
- xAI usage [`ef8ae4db`](https://github.com/Veloera/Veloera/commit/ef8ae4db)
- 优化数据流处理 [`9328b907`](https://github.com/Veloera/Veloera/commit/9328b907)
- Update model ratios for gemini-2.5-pro [`c8694554`](https://github.com/Veloera/Veloera/commit/c8694554)
- gemini test MaxTokens [`f24de656`](https://github.com/Veloera/Veloera/commit/f24de656)
- cannot save OIDC settings [`e34dccbc`](https://github.com/Veloera/Veloera/commit/e34dccbc)
- Improve setup check logic and logging for system initialization [`a6bb30af`](https://github.com/Veloera/Veloera/commit/a6bb30af)
- Update option key from SelfUseModeEnabled to DemoSiteEnabled in PostSetup function [`27933da8`](https://github.com/Veloera/Veloera/commit/27933da8)
- Correct option key for SelfUseModeEnabled in setup controller [`1921ac36`](https://github.com/Veloera/Veloera/commit/1921ac36)
- fixed bug where target.id was null when clicking 'x' icon [`9cfa1387`](https://github.com/Veloera/Veloera/commit/9cfa1387)
- claude function calling type [`20c043f5`](https://github.com/Veloera/Veloera/commit/20c043f5)
- Adjust MaxTokens logic for non-Claude models in test request [`73263e02`](https://github.com/Veloera/Veloera/commit/73263e02)
- wrong thinking labels appear in non-thinking models (#861) [`cc1400e9`](https://github.com/Veloera/Veloera/commit/cc1400e9)
- claude to openai tools use [`c0b93507`](https://github.com/Veloera/Veloera/commit/c0b93507)
- claude to openai tools use [`229738cd`](https://github.com/Veloera/Veloera/commit/229738cd)
- claude to openai tools use [`39d95172`](https://github.com/Veloera/Veloera/commit/39d95172)
- Add error logging for OIDC configuration retrieval [`20e34bec`](https://github.com/Veloera/Veloera/commit/20e34bec)
- Improve mobile layout and scrolling behavior [`3122b8a3`](https://github.com/Veloera/Veloera/commit/3122b8a3)
- Correct typo in group_ratio variable name in LogsTable [`5017fabb`](https://github.com/Veloera/Veloera/commit/5017fabb)
- Add optional chaining to prevent potential undefined errors in LogsTable #833 [`bd5c261b`](https://github.com/Veloera/Veloera/commit/bd5c261b)
- Refine embedding model detection in channel test [`4a8bb625`](https://github.com/Veloera/Veloera/commit/4a8bb625)
- Adjust DeepSeek cache ratio to 0.1 [`8c209e2f`](https://github.com/Veloera/Veloera/commit/8c209e2f)
- Update default cache ratio from 0.5 to 1 [`1f4ebddc`](https://github.com/Veloera/Veloera/commit/1f4ebddc)
- possible incomplete return of the think field and incorrect occurrences of the `reasoning` field [`894dce73`](https://github.com/Veloera/Veloera/commit/894dce73)
- adapting return format for openrouter think content (#793) [`b95142bb`](https://github.com/Veloera/Veloera/commit/b95142bb)
- Handle error in NotifyRootUser and log system errors #812 [`a3739f67`](https://github.com/Veloera/Veloera/commit/a3739f67)
- error NotifyRootUser #812 [`e3f9ef18`](https://github.com/Veloera/Veloera/commit/e3f9ef18)
- Prevent resource leaks by adding body close in stream handlers [`558e625a`](https://github.com/Veloera/Veloera/commit/558e625a)
- vertex claude [`8deab221`](https://github.com/Veloera/Veloera/commit/8deab221)
- #810 [`17e9f1a0`](https://github.com/Veloera/Veloera/commit/17e9f1a0)
- #810 [`792754ce`](https://github.com/Veloera/Veloera/commit/792754ce)
- Ignore EOF errors in OpenAI stream scanner [`eb46b71a`](https://github.com/Veloera/Veloera/commit/eb46b71a)
- Handle scanner errors in OpenAI relay stream handler [`b00dd8b4`](https://github.com/Veloera/Veloera/commit/b00dd8b4)
- vertex claude [`b1be64bc`](https://github.com/Veloera/Veloera/commit/b1be64bc)
- Typo in README [`47d3b515`](https://github.com/Veloera/Veloera/commit/47d3b515)
- channel test model mapped [`760514c3`](https://github.com/Veloera/Veloera/commit/760514c3)
- Use channel group in model testing log record [`2ca0d724`](https://github.com/Veloera/Veloera/commit/2ca0d724)
- Correct option map key for PreConsumedQuota [`a592a81b`](https://github.com/Veloera/Veloera/commit/a592a81b)
- Enhance error message for missing model ratio configuration [`ce185484`](https://github.com/Veloera/Veloera/commit/ce185484)
- Improve error handling for model ratio and price validation #800 [`2f9faba4`](https://github.com/Veloera/Veloera/commit/2f9faba4)
- Improve model ratio and price management [`a5085014`](https://github.com/Veloera/Veloera/commit/a5085014)
- Simplify Claude settings value conversion logic [`d306394f`](https://github.com/Veloera/Veloera/commit/d306394f)
- Prevent duplicate headers in Claude settings [`cdba87a7`](https://github.com/Veloera/Veloera/commit/cdba87a7)
- Update Claude thinking adapter token percentage input guidance [`4784ca75`](https://github.com/Veloera/Veloera/commit/4784ca75)
- Correct model request configuration in Vertex Claude adaptor [`3a18c0ce`](https://github.com/Veloera/Veloera/commit/3a18c0ce)
- Add pagination support to user search functionality [`0f1c4c4e`](https://github.com/Veloera/Veloera/commit/0f1c4c4e)
- Improve AWS Claude adaptor request conversion error handling #796 [`5f0b3f6d`](https://github.com/Veloera/Veloera/commit/5f0b3f6d)
- gemini&claude tool call format #795 #766 [`13ab0f8e`](https://github.com/Veloera/Veloera/commit/13ab0f8e)
- claude tool call format #795 #766 [`6d8d40e6`](https://github.com/Veloera/Veloera/commit/6d8d40e6)
- Update Gemini safety settings to use 'OFF' as default [`c802b3b4`](https://github.com/Veloera/Veloera/commit/c802b3b4)
- Update Gemini safety settings category [`ed4e1c23`](https://github.com/Veloera/Veloera/commit/ed4e1c23)
- Update Gemini safety settings default value [`e581ea33`](https://github.com/Veloera/Veloera/commit/e581ea33)
- Adjust Claude thinking mode request parameters [`da4d1861`](https://github.com/Veloera/Veloera/commit/da4d1861)
- Improve 429 error logging with detailed message [`cc1d6e1c`](https://github.com/Veloera/Veloera/commit/cc1d6e1c)
- mistral [`88a2fec1`](https://github.com/Veloera/Veloera/commit/88a2fec1)
- fix image ratio calculation [`27ea231d`](https://github.com/Veloera/Veloera/commit/27ea231d)
- Ensure correct quota warning threshold type conversion [`48926b8a`](https://github.com/Veloera/Veloera/commit/48926b8a)
- ShouldDisableChannel [`7a13fab2`](https://github.com/Veloera/Veloera/commit/7a13fab2)
- mistral adaptor (close #774) [`bf75b308`](https://github.com/Veloera/Veloera/commit/bf75b308)
- Correct Ollama channel authentication header setting [`60aac77c`](https://github.com/Veloera/Veloera/commit/60aac77c)
- Fix Ollama channel authentication [`a13f4d6c`](https://github.com/Veloera/Veloera/commit/a13f4d6c)
- Remove redundant error handling in distributor and relay modules [`ae5875d4`](https://github.com/Veloera/Veloera/commit/ae5875d4)
- Extend temperature handling for OpenAI-like models [`812c188a`](https://github.com/Veloera/Veloera/commit/812c188a)
- Improve OpenAI stream data parsing and handling [`bd4ce9cd`](https://github.com/Veloera/Veloera/commit/bd4ce9cd)
- Update BaseURL placeholder text and label in channel edit page [`871d73ec`](https://github.com/Veloera/Veloera/commit/871d73ec)
- adjust max tokens configuration in test request builder [`cf0ff037`](https://github.com/Veloera/Veloera/commit/cf0ff037)
- update session store configuration [`bbc1550a`](https://github.com/Veloera/Veloera/commit/bbc1550a)
- always use modelMapping in channel test [`882c5970`](https://github.com/Veloera/Veloera/commit/882c5970)
- replace context-based user ID with session-based retrieval #741 [`8418dbe7`](https://github.com/Veloera/Veloera/commit/8418dbe7)
- CI #744 [`68c559c1`](https://github.com/Veloera/Veloera/commit/68c559c1)
- correct JSON tags for `Prompt` and `Suffix` in `GeneralOpenAIRequest` [`1eb72f2f`](https://github.com/Veloera/Veloera/commit/1eb72f2f)
- channels model_mapping [`70083ecd`](https://github.com/Veloera/Veloera/commit/70083ecd)
- update logs table total count display [`f7a4016d`](https://github.com/Veloera/Veloera/commit/f7a4016d)
- improve reasoning effort model suffix handling [`a29e1e0a`](https://github.com/Veloera/Veloera/commit/a29e1e0a)
- update reasoning effort model suffix parsing [`ce77f255`](https://github.com/Veloera/Veloera/commit/ce77f255)
- clear channel name in user logs [`c511c7f0`](https://github.com/Veloera/Veloera/commit/c511c7f0)
- update DeepSeek reasoner model ratio check [`15918b20`](https://github.com/Veloera/Veloera/commit/15918b20)
- display docker build error [`87052e92`](https://github.com/Veloera/Veloera/commit/87052e92)
- remove ffmpeg-tools [`c5b151ed`](https://github.com/Veloera/Veloera/commit/c5b151ed)
- log filename format [`da83de27`](https://github.com/Veloera/Veloera/commit/da83de27)
- incorrect whisper audio usage [`2abf05b3`](https://github.com/Veloera/Veloera/commit/2abf05b3)
- fix the issue of fetching model list failure in batch add channel [`2ec4d284`](https://github.com/Veloera/Veloera/commit/2ec4d284)
- update linux-release workflow to install gcc-aarch64-linux-gnu non-interactively [`055a238e`](https://github.com/Veloera/Veloera/commit/055a238e)
- update iframe styling and permissions in ChatPage component [`af0b9325`](https://github.com/Veloera/Veloera/commit/af0b9325)
- retry prompt tokens [`ef4c1a2e`](https://github.com/Veloera/Veloera/commit/ef4c1a2e)
- try to fix pgsql #685 [`42bf95bd`](https://github.com/Veloera/Veloera/commit/42bf95bd)
- error page size opts [`e6ea5e59`](https://github.com/Veloera/Veloera/commit/e6ea5e59)
- try to fix pgsql #682 [`a7e5f1e5`](https://github.com/Veloera/Veloera/commit/a7e5f1e5)
- try to fix pgsql #682 [`87d5e286`](https://github.com/Veloera/Veloera/commit/87d5e286)
- Gemini 其他文件类型的支持（Base64URL） [`2a15dfcc`](https://github.com/Veloera/Veloera/commit/2a15dfcc)
- Gemini 函数调用的文本转义 [`9e5a7ed5`](https://github.com/Veloera/Veloera/commit/9e5a7ed5)
- playground request_start_time [`65d1cde8`](https://github.com/Veloera/Veloera/commit/65d1cde8)
- prevent setting models to null in PersonalSetting component [`8f4a2df5`](https://github.com/Veloera/Veloera/commit/8f4a2df5)
- 转义 Gemini 工具调用中的反斜杠 [`72dc5430`](https://github.com/Veloera/Veloera/commit/72dc5430)
- add index in the tool calls when chat by stream (gemini) [`38cff317`](https://github.com/Veloera/Veloera/commit/38cff317)
- prevent duplicate models in user group retrieval [`1cef91a7`](https://github.com/Veloera/Veloera/commit/1cef91a7)
- oauth bind [`0fd0e5d3`](https://github.com/Veloera/Veloera/commit/0fd0e5d3)
- update render function for quota display in Detail page [`f92d96e2`](https://github.com/Veloera/Veloera/commit/f92d96e2)
- validate number input in renderQuotaNumberWithDigit and improve data handling in Detail page [`bfba4866`](https://github.com/Veloera/Veloera/commit/bfba4866)
- update MaxCompletionTokens for model prefix handling in buildTestRequest function [`ed4d26fc`](https://github.com/Veloera/Veloera/commit/ed4d26fc)
- correct user retrieval in GetPricing function [`ba56e2e8`](https://github.com/Veloera/Veloera/commit/ba56e2e8)
- resolve pricing calculation issue (#659) [`7c20e6d0`](https://github.com/Veloera/Veloera/commit/7c20e6d0)
- update searchUsers function to include searchKeyword and searchGroup parameters [`f2c93881`](https://github.com/Veloera/Veloera/commit/f2c93881)
- #657 [`fe2165ac`](https://github.com/Veloera/Veloera/commit/fe2165ac)
- get upstream models [`3003d12a`](https://github.com/Veloera/Veloera/commit/3003d12a)
- gemini func call [`d40e6ec2`](https://github.com/Veloera/Veloera/commit/d40e6ec2)
- mutil func call in gemini [`a4795737`](https://github.com/Veloera/Veloera/commit/a4795737)
- Fix the issue where Gemini loses content when converting OpenAI format in the stream. [`158edab9`](https://github.com/Veloera/Veloera/commit/158edab9)
- 修复添加模型切换模式时，初始化空值导致的判断问题 [`e3a01622`](https://github.com/Veloera/Veloera/commit/e3a01622)
- Correct JSON field name for SystemInstructions in GeminiChatRequest [`76acfdc9`](https://github.com/Veloera/Veloera/commit/76acfdc9)
- Refine sider visibility logic in HeaderBar component [`a485db50`](https://github.com/Veloera/Veloera/commit/a485db50)
- Adjust inner padding style in PageLayout component [`2a6f3ad2`](https://github.com/Veloera/Veloera/commit/2a6f3ad2)
- Refine sider visibility and inner padding logic in StyleProvider component [`16599a90`](https://github.com/Veloera/Veloera/commit/16599a90)
- Update label truncation logic in Playground and adjust sider visibility in HeaderBar [`d241e4fe`](https://github.com/Veloera/Veloera/commit/d241e4fe)
- Correct inner padding and sider visibility logic in HeaderBar, PageLayout, and SiderBar components [`e17f36e7`](https://github.com/Veloera/Veloera/commit/e17f36e7)
- 编辑标签文字错误 [`5d1087a6`](https://github.com/Veloera/Veloera/commit/5d1087a6)
- edit channel weight and priority [`cf8b30ed`](https://github.com/Veloera/Veloera/commit/cf8b30ed)
- 渠道标签开启下使用ID排序出错 [`56ccb30a`](https://github.com/Veloera/Veloera/commit/56ccb30a)
- telegram register [`dd293f80`](https://github.com/Veloera/Veloera/commit/dd293f80)
- 关键词搜索时无法展开测试模型 [`3aa59178`](https://github.com/Veloera/Veloera/commit/3aa59178)
- 标签分组功能开启时无法展开测试模型 [`b5d273b6`](https://github.com/Veloera/Veloera/commit/b5d273b6)
- email panic [`d9b622c8`](https://github.com/Veloera/Veloera/commit/d9b622c8)
- 360智能接口地址更新 [`28885fee`](https://github.com/Veloera/Veloera/commit/28885fee)
- realtime [`6d4edc1f`](https://github.com/Veloera/Veloera/commit/6d4edc1f)
- tag channel copy [`bb0c5047`](https://github.com/Veloera/Veloera/commit/bb0c5047)
- xAI missing finish_reason #572 [`1774be85`](https://github.com/Veloera/Veloera/commit/1774be85)
- stt模型计费 [`7ebc1cfb`](https://github.com/Veloera/Veloera/commit/7ebc1cfb)
- search channel #442 [`807385d3`](https://github.com/Veloera/Veloera/commit/807385d3)
- oauth aff [`334a2424`](https://github.com/Veloera/Veloera/commit/334a2424)
- oauth aff [`7db70337`](https://github.com/Veloera/Veloera/commit/7db70337)
- aws claude [`320e6ec5`](https://github.com/Veloera/Veloera/commit/320e6ec5)
- LinuxDo OAuth [`85b90e89`](https://github.com/Veloera/Veloera/commit/85b90e89)
- 非root日志展开bug [`34998f79`](https://github.com/Veloera/Veloera/commit/34998f79)
- returnPreConsumedQuota [`8fc49f98`](https://github.com/Veloera/Veloera/commit/8fc49f98)
- log table unknown ws prop error [`afc1e92e`](https://github.com/Veloera/Veloera/commit/afc1e92e)
- mistral adaptor [`7b1ff41e`](https://github.com/Veloera/Veloera/commit/7b1ff41e)
- realtime计费 [`4e0c522c`](https://github.com/Veloera/Veloera/commit/4e0c522c)
- channel test [`f08f7ae9`](https://github.com/Veloera/Veloera/commit/f08f7ae9)
- 修复ws 握手失败、计费问题 [`be64408a`](https://github.com/Veloera/Veloera/commit/be64408a)
- 部分情况缺少返回预扣 [`f0907bf6`](https://github.com/Veloera/Veloera/commit/f0907bf6)
- 修复聊天环境变量替换不完全 (close #542) [`3e2ae29b`](https://github.com/Veloera/Veloera/commit/3e2ae29b)
- 修复Playground分组无用户分组 (close #529) [`ade6d0f5`](https://github.com/Veloera/Veloera/commit/ade6d0f5)
- 修复用户可选分组不能选择用户分组 (close #528) [`f599c659`](https://github.com/Veloera/Veloera/commit/f599c659)
- 修复自定义聊天bug [`40baa636`](https://github.com/Veloera/Veloera/commit/40baa636)
- playground group [`6a8a4bcf`](https://github.com/Veloera/Veloera/commit/6a8a4bcf)
- playground气泡溢出 #511 [`74631124`](https://github.com/Veloera/Veloera/commit/74631124)
- playground max_tokens #512 #511 [`f9ba10f1`](https://github.com/Veloera/Veloera/commit/f9ba10f1)
- 第三方登录注销 #500 [`9fe1f35f`](https://github.com/Veloera/Veloera/commit/9fe1f35f)
- 第三方登录注销 #500 [`972ac1ee`](https://github.com/Veloera/Veloera/commit/972ac1ee)
- error user role [`221894d9`](https://github.com/Veloera/Veloera/commit/221894d9)
- 使用令牌分组时 "/v1/models" 返回模型不正确 #481 [`af7fecbf`](https://github.com/Veloera/Veloera/commit/af7fecbf)
- token group #477 [`3fbdd502`](https://github.com/Veloera/Veloera/commit/3fbdd502)
- 初始令牌 [`9032b5cf`](https://github.com/Veloera/Veloera/commit/9032b5cf)
- '/v1/models' #474 [`a03cd155`](https://github.com/Veloera/Veloera/commit/a03cd155)
- '/v1/models' #474 [`02f51377`](https://github.com/Veloera/Veloera/commit/02f51377)
- '/vi/models' #474 [`e6df0ed2`](https://github.com/Veloera/Veloera/commit/e6df0ed2)
- cohere SafetyMode [`d168a685`](https://github.com/Veloera/Veloera/commit/d168a685)
- tool use in claude [`0ada2371`](https://github.com/Veloera/Veloera/commit/0ada2371)
- email [`8bc1e956`](https://github.com/Veloera/Veloera/commit/8bc1e956)
- channel auto ban [`46e03683`](https://github.com/Veloera/Veloera/commit/46e03683)
- channel auto ban #443 [`ff0985f0`](https://github.com/Veloera/Veloera/commit/ff0985f0)
- 修复 dall-e-2 请求报错 [`967ccabb`](https://github.com/Veloera/Veloera/commit/967ccabb)
- 多地区outlook邮箱和ofb邮箱Auth [`484a8595`](https://github.com/Veloera/Veloera/commit/484a8595)
- add email missing Message-ID [`a5ec11e4`](https://github.com/Veloera/Veloera/commit/a5ec11e4)
- lobechat #430 [`b3d8e3e9`](https://github.com/Veloera/Veloera/commit/b3d8e3e9)
- log page 'Cannot read properties of undefined (reading 'length')' [`8cd8cc29`](https://github.com/Veloera/Veloera/commit/8cd8cc29)
- log pagination [`9edb7c4a`](https://github.com/Veloera/Veloera/commit/9edb7c4a)
- log分页问题 [`a2af637e`](https://github.com/Veloera/Veloera/commit/a2af637e)
- close #422 [`9e45dbe9`](https://github.com/Veloera/Veloera/commit/9e45dbe9)
- sqlite group 查询兼容 [`9452be51`](https://github.com/Veloera/Veloera/commit/9452be51)
- 修复mysql兼容问题 [`04f0084d`](https://github.com/Veloera/Veloera/commit/04f0084d)
- response format [`2e3c266b`](https://github.com/Veloera/Veloera/commit/2e3c266b)
- optionList bug [`e614ca37`](https://github.com/Veloera/Veloera/commit/e614ca37)
- 渠道多分组，优化分组 like 查询 [`190316f6`](https://github.com/Veloera/Veloera/commit/190316f6)
- epay [`5d0d268c`](https://github.com/Veloera/Veloera/commit/5d0d268c)
- channel typ error [`0b4ef42d`](https://github.com/Veloera/Veloera/commit/0b4ef42d)
- 重试后request id不一致 [`0123ad4d`](https://github.com/Veloera/Veloera/commit/0123ad4d)
- rpm模糊查询 [`f8f15bd1`](https://github.com/Veloera/Veloera/commit/f8f15bd1)
- 日志模糊查询 [`b7690fe1`](https://github.com/Veloera/Veloera/commit/b7690fe1)
- error channel name on notify. #338 [`1501ccb9`](https://github.com/Veloera/Veloera/commit/1501ccb9)
- panic when get model ratio (close #392) [`b16e6bf4`](https://github.com/Veloera/Veloera/commit/b16e6bf4)
- image quota (close #382) [`9e610c94`](https://github.com/Veloera/Veloera/commit/9e610c94)
- gemini [`b8291dcd`](https://github.com/Veloera/Veloera/commit/b8291dcd)
- first login error (close #385) [`caaecb8d`](https://github.com/Veloera/Veloera/commit/caaecb8d)
- the base64 format image_url for gemini [`b9454c3f`](https://github.com/Veloera/Veloera/commit/b9454c3f)
- 修复aws claude渠道panic的问题 [`96bdf971`](https://github.com/Veloera/Veloera/commit/96bdf971)
- gemini stream finish reason (close #378) [`3875b141`](https://github.com/Veloera/Veloera/commit/3875b141)
- 修复nginx缓存导致串用户问题 [`ce815a98`](https://github.com/Veloera/Veloera/commit/ce815a98)
- image relay quota [`70491ea1`](https://github.com/Veloera/Veloera/commit/70491ea1)
- distribute panic [`7a0beb57`](https://github.com/Veloera/Veloera/commit/7a0beb57)
- try to fix panic #369 [`e3b83f88`](https://github.com/Veloera/Veloera/commit/e3b83f88)
- embedding model dimensions [`fd872602`](https://github.com/Veloera/Veloera/commit/fd872602)
- try to fix panic #369 [`4d0d1893`](https://github.com/Veloera/Veloera/commit/4d0d1893)
- fix bug [`86ca533f`](https://github.com/Veloera/Veloera/commit/86ca533f)
- try to fix mj [`ba27da9e`](https://github.com/Veloera/Veloera/commit/ba27da9e)
- 日志详情非消费类型显示错误 [`9bbe8e7d`](https://github.com/Veloera/Veloera/commit/9bbe8e7d)
- openai stream response [`e2b90616`](https://github.com/Veloera/Veloera/commit/e2b90616)
- openai response time [`220ab412`](https://github.com/Veloera/Veloera/commit/220ab412)
- azure stream options [`0f687aab`](https://github.com/Veloera/Veloera/commit/0f687aab)
- http code is not properly disabled [`d55cb35c`](https://github.com/Veloera/Veloera/commit/d55cb35c)
- channel timeout auto-ban and auto-enable [`e67aa370`](https://github.com/Veloera/Veloera/commit/e67aa370)
- gemini usage (close #354) [`4e7e2062`](https://github.com/Veloera/Veloera/commit/4e7e2062)
- dify (close #355) [`579fc812`](https://github.com/Veloera/Veloera/commit/579fc812)
- email login [`f55f63f4`](https://github.com/Veloera/Veloera/commit/f55f63f4)
- hunyuan [`b75134ec`](https://github.com/Veloera/Veloera/commit/b75134ec)
- stream options [`a0755987`](https://github.com/Veloera/Veloera/commit/a0755987)
- baidu max_output_tokens (#353) [`90abe7f2`](https://github.com/Veloera/Veloera/commit/90abe7f2)
- baidu max_output_tokens (close #353) [`02545e48`](https://github.com/Veloera/Veloera/commit/02545e48)
- channel default test model [`49cec509`](https://github.com/Veloera/Veloera/commit/49cec509)
- 修复渠道晒筛选后无法展开测试模型 (close #297 #302) [`4f6710e5`](https://github.com/Veloera/Veloera/commit/4f6710e5)
- 日志详情 [`e062cf32`](https://github.com/Veloera/Veloera/commit/e062cf32)
- streaming timeout [`4246c4cd`](https://github.com/Veloera/Veloera/commit/4246c4cd)
- streaming timeout [`1e536ee7`](https://github.com/Veloera/Veloera/commit/1e536ee7)
- fix rerank [`1735e093`](https://github.com/Veloera/Veloera/commit/1735e093)
- midjourney channel auto ban [`afe02c6a`](https://github.com/Veloera/Veloera/commit/afe02c6a)
- try to fix tencent hunyuan #336 [`6c5b3b51`](https://github.com/Veloera/Veloera/commit/6c5b3b51)
- channel auto ban [`d4578e28`](https://github.com/Veloera/Veloera/commit/d4578e28)
- sqlite too many SQL variables [`1e9d64fd`](https://github.com/Veloera/Veloera/commit/1e9d64fd)
- mj auto ban [`4d3b57e1`](https://github.com/Veloera/Veloera/commit/4d3b57e1)
- try to fix minimax (close #327) [`11be36da`](https://github.com/Veloera/Veloera/commit/11be36da)
- try to fix hunyuan (close #303) [`77d14561`](https://github.com/Veloera/Veloera/commit/77d14561)
- auto ban [`5abb0a9c`](https://github.com/Veloera/Veloera/commit/5abb0a9c)
- claude usage [`954fa879`](https://github.com/Veloera/Veloera/commit/954fa879)
- Available models could not be populated when adding a new channel [`6f1bef66`](https://github.com/Veloera/Veloera/commit/6f1bef66)
- 删除用户改为注销 [`692455ef`](https://github.com/Veloera/Veloera/commit/692455ef)
- typo [`c1040afe`](https://github.com/Veloera/Veloera/commit/c1040afe)
- GetLogByKey panic [`a31247ec`](https://github.com/Veloera/Veloera/commit/a31247ec)
- pricing page group ratio (close #275) [`3cd25c7e`](https://github.com/Veloera/Veloera/commit/3cd25c7e)
- log page error [`f07ae813`](https://github.com/Veloera/Veloera/commit/f07ae813)
- 模型价格 [`cf8fe63f`](https://github.com/Veloera/Veloera/commit/cf8fe63f)
- 模型价格 [`1568d648`](https://github.com/Veloera/Veloera/commit/1568d648)
- log page error (close #270) [`51e0754a`](https://github.com/Veloera/Veloera/commit/51e0754a)
- try to fix sqlite database migration (#231) [`7003a4ed`](https://github.com/Veloera/Veloera/commit/7003a4ed)
- gpt-4-gizmo-* model ratio [`9f18641d`](https://github.com/Veloera/Veloera/commit/9f18641d)
- 修复自定义渠道出错 #243 [`fd19798c`](https://github.com/Veloera/Veloera/commit/fd19798c)
- 修复"/v1/models"不显示自定义模型 （close #235) [`db575a1c`](https://github.com/Veloera/Veloera/commit/db575a1c)
- update user (#230) [`2bf40450`](https://github.com/Veloera/Veloera/commit/2bf40450)
- aws claude system [`7fb6420e`](https://github.com/Veloera/Veloera/commit/7fb6420e)
- aws claude [`5425b5bf`](https://github.com/Veloera/Veloera/commit/5425b5bf)
- 规范claude返回格式 [`1c6fd879`](https://github.com/Veloera/Veloera/commit/1c6fd879)
- 规范claude返回格式 [`d1c89478`](https://github.com/Veloera/Veloera/commit/d1c89478)
- claude流模式缺失role [`7d2d5250`](https://github.com/Veloera/Veloera/commit/7d2d5250)
- claude [`e2edd5e7`](https://github.com/Veloera/Veloera/commit/e2edd5e7)
- update user [`08b53364`](https://github.com/Veloera/Veloera/commit/08b53364)
- audio预扣费未返还 [`b1b38a6b`](https://github.com/Veloera/Veloera/commit/b1b38a6b)
- close #218 [`a3ccc92f`](https://github.com/Veloera/Veloera/commit/a3ccc92f)
- fix update payment setting [`77e7d111`](https://github.com/Veloera/Veloera/commit/77e7d111)
- claude max_tokens [`53e87900`](https://github.com/Veloera/Veloera/commit/53e87900)
- test all channel error (close #206) [`a4defe6a`](https://github.com/Veloera/Veloera/commit/a4defe6a)
- the dark mode does not work for the `OperationSetting` and `SystemSetting` panels [`848358d8`](https://github.com/Veloera/Veloera/commit/848358d8)
- Gemini 1.5 name error [`d7e117ac`](https://github.com/Veloera/Veloera/commit/d7e117ac)
- rename the latest Gemini model name [`3b6ea510`](https://github.com/Veloera/Veloera/commit/3b6ea510)
- 修复渠道一次性添加很多model失败 [`320da09f`](https://github.com/Veloera/Veloera/commit/320da09f)
- 307本地重试 [`2d849e0d`](https://github.com/Veloera/Veloera/commit/2d849e0d)
- distributor panic [`60d7ed3f`](https://github.com/Veloera/Veloera/commit/60d7ed3f)
- select channel [`34bf8f89`](https://github.com/Veloera/Veloera/commit/34bf8f89)
- email whitelist check [`257cfc23`](https://github.com/Veloera/Veloera/commit/257cfc23)
- add group tag 'unknown' [`fc9f8c8e`](https://github.com/Veloera/Veloera/commit/fc9f8c8e)
- GetRandomSatisfiedChannel [`aaf3a1f0`](https://github.com/Veloera/Veloera/commit/aaf3a1f0)
- email whitelist check [`9025756b`](https://github.com/Veloera/Veloera/commit/9025756b)
- user update error [`2ea60099`](https://github.com/Veloera/Veloera/commit/2ea60099)
- log page type error (close #154) [`a33f685f`](https://github.com/Veloera/Veloera/commit/a33f685f)
- update user quote (close #161) [`5ce8e6da`](https://github.com/Veloera/Veloera/commit/5ce8e6da)
- add key prop to Tag components [`d6e373fb`](https://github.com/Veloera/Veloera/commit/d6e373fb)
- SearchUsers (close #160) [`36d164be`](https://github.com/Veloera/Veloera/commit/36d164be)
- remove sensitive check on completion (close #157) [`44a8ade4`](https://github.com/Veloera/Veloera/commit/44a8ade4)
- 支持 /mj-{mode} 路径 [`3065bf92`](https://github.com/Veloera/Veloera/commit/3065bf92)
- 支持 /mj-{mode} 路径 [`2e595bda`](https://github.com/Veloera/Veloera/commit/2e595bda)
- ollama channel test [`319e97d6`](https://github.com/Veloera/Veloera/commit/319e97d6)
- CountTokenInput [`6114c9bb`](https://github.com/Veloera/Veloera/commit/6114c9bb)
- CountTokenInput [`3cf2f0d5`](https://github.com/Veloera/Veloera/commit/3cf2f0d5)
- try to fix 307 [`cc8cc8b3`](https://github.com/Veloera/Veloera/commit/cc8cc8b3)
- 无法复制弹窗过小 [`b2a280c1`](https://github.com/Veloera/Veloera/commit/b2a280c1)
- Cannot read properties of undefined (reading 'map') (close #148) [`3800dc21`](https://github.com/Veloera/Veloera/commit/3800dc21)
- error in console under dev mode [`f671176d`](https://github.com/Veloera/Veloera/commit/f671176d)
- 流模式网络错误导致0补 [`2d36dee1`](https://github.com/Veloera/Veloera/commit/2d36dee1)
- 模型倍率和价格无法设置 [`6eb30ec3`](https://github.com/Veloera/Veloera/commit/6eb30ec3)
- 修复默认模型倍率未显示 [`0b3520e3`](https://github.com/Veloera/Veloera/commit/0b3520e3)
- ci [`66e30f41`](https://github.com/Veloera/Veloera/commit/66e30f41)
- embed [`21f48b55`](https://github.com/Veloera/Veloera/commit/21f48b55)
- 模型固定价格为空时错误使用默认价格 [`67332bc8`](https://github.com/Veloera/Veloera/commit/67332bc8)
- GLM-4V 的 Vision 兼容问题 (close #136) [`d0acecb2`](https://github.com/Veloera/Veloera/commit/d0acecb2)
- mj [`a70ca534`](https://github.com/Veloera/Veloera/commit/a70ca534)
- 充值并发导致订单号相同 [`c33b1522`](https://github.com/Veloera/Veloera/commit/c33b1522)
- add missing created [`ff7da08b`](https://github.com/Veloera/Veloera/commit/ff7da08b)
- add missing id,object,created [`3e03c5a7`](https://github.com/Veloera/Veloera/commit/3e03c5a7)
- try to fix curl: (18) [`d9344d79`](https://github.com/Veloera/Veloera/commit/d9344d79)
- fix embedding [`c4b3d3a9`](https://github.com/Veloera/Veloera/commit/c4b3d3a9)
- add missing version [`3f808be2`](https://github.com/Veloera/Veloera/commit/3f808be2)
- fix mj panic [`9b64f4a3`](https://github.com/Veloera/Veloera/commit/9b64f4a3)
- fix SensitiveWords error [`222a5538`](https://github.com/Veloera/Veloera/commit/222a5538)
- fix SensitiveWords load error [`d7e25e16`](https://github.com/Veloera/Veloera/commit/d7e25e16)
- 敏感词库为空时，不检测 [`c7658b70`](https://github.com/Veloera/Veloera/commit/c7658b70)
- midjourneys table [`d5e93e78`](https://github.com/Veloera/Veloera/commit/d5e93e78)
- claude panic [`dd719460`](https://github.com/Veloera/Veloera/commit/dd719460)
- claude panic [`b736de71`](https://github.com/Veloera/Veloera/commit/b736de71)
- fix SensitiveWordContains not working [`eb6257a8`](https://github.com/Veloera/Veloera/commit/eb6257a8)
- fix error [`47b9f485`](https://github.com/Veloera/Veloera/commit/47b9f485)
- api type error [`4c4e0870`](https://github.com/Veloera/Veloera/commit/4c4e0870)
- make the 'openai_organization' parameter actually work. [`f63ad9c0`](https://github.com/Veloera/Veloera/commit/f63ad9c0)
- make the 'openai_organization' parameter actually work. [`0fb98e44`](https://github.com/Veloera/Veloera/commit/0fb98e44)
- chatglm top_p error (close #124) [`b8c053c3`](https://github.com/Veloera/Veloera/commit/b8c053c3)
- 侧边栏聚焦问题 (close #123) [`2581b373`](https://github.com/Veloera/Veloera/commit/2581b373)
- 修复日志翻页问题 (close #122) [`f65477d0`](https://github.com/Veloera/Veloera/commit/f65477d0)
- fix image-seed error [`6ab1b3a5`](https://github.com/Veloera/Veloera/commit/6ab1b3a5)
- 修复epay可能重复回调的问题 [`299911d4`](https://github.com/Veloera/Veloera/commit/299911d4)
- reroll action error [`a77fbc0f`](https://github.com/Veloera/Veloera/commit/a77fbc0f)
- "Inpaint" code error [`44361d75`](https://github.com/Veloera/Veloera/commit/44361d75)
- fix typo [`d3399d68`](https://github.com/Veloera/Veloera/commit/d3399d68)
- layout issues [`30d48ea4`](https://github.com/Veloera/Veloera/commit/30d48ea4)
- 修复模型映射功能失效 (close #105) [`d4df1960`](https://github.com/Veloera/Veloera/commit/d4df1960)
- 请求阿里通义千问异常 (close #108) [`97dd8054`](https://github.com/Veloera/Veloera/commit/97dd8054)
- 删除用户改为软删除 (close #107) [`bf241b21`](https://github.com/Veloera/Veloera/commit/bf241b21)
- 修复claude渠道流模式计费可能异常 [`7ab6c6c3`](https://github.com/Veloera/Veloera/commit/7ab6c6c3)
- fix gemini channel test [`8fe8340b`](https://github.com/Veloera/Veloera/commit/8fe8340b)
- fix claude channel test [`26ef906c`](https://github.com/Veloera/Veloera/commit/26ef906c)
- fix claude 3 request missing the 'max_token' field [`f43b2685`](https://github.com/Veloera/Veloera/commit/f43b2685)
- fix gemini [`3c3c5305`](https://github.com/Veloera/Veloera/commit/3c3c5305)
- 隐藏无用按钮 [`6a83c8ad`](https://github.com/Veloera/Veloera/commit/6a83c8ad)
- fix Azure channel test (close #101) [`0640dd81`](https://github.com/Veloera/Veloera/commit/0640dd81)
- fix bug [`0e06be8c`](https://github.com/Veloera/Veloera/commit/0e06be8c)
- 修复流模式错误扣费的问题 (close #95) [`626217fb`](https://github.com/Veloera/Veloera/commit/626217fb)
- 恢复微信公众号扫码功能 (close #7) [`9de2d21e`](https://github.com/Veloera/Veloera/commit/9de2d21e)
- Fix the issue of 'unknown relay mode' when making an embedding request (close #93) [`fb39b6b3`](https://github.com/Veloera/Veloera/commit/fb39b6b3)
- model names must not contain spaces at both ends [`d160736a`](https://github.com/Veloera/Veloera/commit/d160736a)
- restore the set of StatusContext [`5cb933a2`](https://github.com/Veloera/Veloera/commit/5cb933a2)
- 修复保存telegram设置后会将telegram登陆禁用的问题 [`1f0a48a8`](https://github.com/Veloera/Veloera/commit/1f0a48a8)
- 修复预扣费判定无效导致用户可无限欠费问题 [`37b307a7`](https://github.com/Veloera/Veloera/commit/37b307a7)
- 修复添加和编辑渠道无可选择模型 [`54088bc6`](https://github.com/Veloera/Veloera/commit/54088bc6)
- remove useless code [`06b746a7`](https://github.com/Veloera/Veloera/commit/06b746a7)
- Improve handling of small weights in channel selection logic [`4c43012e`](https://github.com/Veloera/Veloera/commit/4c43012e)
- add missing UpstreamModelName [`cbe0a1fa`](https://github.com/Veloera/Veloera/commit/cbe0a1fa)
- add missing upstreamModelName [`7297c626`](https://github.com/Veloera/Veloera/commit/7297c626)
- the content displayed on the homepage is incorrect [`ac64fd26`](https://github.com/Veloera/Veloera/commit/ac64fd26)
- fix typo [`2b076eae`](https://github.com/Veloera/Veloera/commit/2b076eae)
- fix preConsumeQuota error [`a3687b72`](https://github.com/Veloera/Veloera/commit/a3687b72)
- fix typo [`9b421478`](https://github.com/Veloera/Veloera/commit/9b421478)
- 修复测试渠道指定模型时实际使用gpt-3.5-turbo的问题 (close #67) [`569751f5`](https://github.com/Veloera/Veloera/commit/569751f5)
- 修复redis缓存用户额度不过期的问题 [`5482fff6`](https://github.com/Veloera/Veloera/commit/5482fff6)
- 修正 Unicode 转义序列解析问题，close #63 [`1cde58c0`](https://github.com/Veloera/Veloera/commit/1cde58c0)
- fix test channel [`f19b11be`](https://github.com/Veloera/Veloera/commit/f19b11be)
- support AI Gateway, close #52 [`0004c102`](https://github.com/Veloera/Veloera/commit/0004c102)
- support AI Gateway, close #52 [`4274b925`](https://github.com/Veloera/Veloera/commit/4274b925)
- 修复是否自动禁用选项不生效 [`166770ee`](https://github.com/Veloera/Veloera/commit/166770ee)
- fix tool calls [`6d047963`](https://github.com/Veloera/Veloera/commit/6d047963)
- fix redis error [`49209229`](https://github.com/Veloera/Veloera/commit/49209229)
- fix redis error [`70bdb8ca`](https://github.com/Veloera/Veloera/commit/70bdb8ca)
- mj 错误处理 [`d3e070d9`](https://github.com/Veloera/Veloera/commit/d3e070d9)
- 修复数据看板渲染错误 [`a8715c61`](https://github.com/Veloera/Veloera/commit/a8715c61)
- 修复渠道一致性问题 [`2b3539fc`](https://github.com/Veloera/Veloera/commit/2b3539fc)
- 修复渠道一致性问题 [`e2a1caba`](https://github.com/Veloera/Veloera/commit/e2a1caba)
- delete RelayPanicRecover [`075b1ac1`](https://github.com/Veloera/Veloera/commit/075b1ac1)
- 修复mj错误返还费用问题 [`2a9c3ac6`](https://github.com/Veloera/Veloera/commit/2a9c3ac6)
- the Redis problem in the CacheGetUsername function [`6aadcf0c`](https://github.com/Veloera/Veloera/commit/6aadcf0c)
- user group size [`ad857928`](https://github.com/Veloera/Veloera/commit/ad857928)
- token model limit [`521ef5e2`](https://github.com/Veloera/Veloera/commit/521ef5e2)
- 完善令牌预扣费逻辑 [`64e9e9cc`](https://github.com/Veloera/Veloera/commit/64e9e9cc)
- fix relay openai panic [`701a28d0`](https://github.com/Veloera/Veloera/commit/701a28d0)
- 修复高并发下，高额度用户使用低额度令牌没有预扣费的问题 [`a3b726dd`](https://github.com/Veloera/Veloera/commit/a3b726dd)
- fix response choice json [`042d55cf`](https://github.com/Veloera/Veloera/commit/042d55cf)
- do not consume user quota if failed [`3432d9e0`](https://github.com/Veloera/Veloera/commit/3432d9e0)
- fix channel panic [`3e13810c`](https://github.com/Veloera/Veloera/commit/3e13810c)
- add Tools support [`db29c963`](https://github.com/Veloera/Veloera/commit/db29c963)
- 修复数据看板筛选用户的时候不能指定时间的问题 [`6ed365c2`](https://github.com/Veloera/Veloera/commit/6ed365c2)
- fix JSON tag in Log struct [`daf0be49`](https://github.com/Veloera/Veloera/commit/daf0be49)
- 减少username和model_name字段长度为64 [`f3124e72`](https://github.com/Veloera/Veloera/commit/f3124e72)
- 减少group和model字段长度为64 [`ebedf2cb`](https://github.com/Veloera/Veloera/commit/ebedf2cb)
- 修复侧边导航栏需要刷新才出现选项的问题 [`18e9d370`](https://github.com/Veloera/Veloera/commit/18e9d370)
- 修复mj固定价格设置无效的问题 [`e2d994d7`](https://github.com/Veloera/Veloera/commit/e2d994d7)
- 数据面板次数统计错误 [`29dbdf01`](https://github.com/Veloera/Veloera/commit/29dbdf01)
- recover panic [`64862cd6`](https://github.com/Veloera/Veloera/commit/64862cd6)
- 索引长度 [`a5e0f86c`](https://github.com/Veloera/Veloera/commit/a5e0f86c)
- 索引名称长度 [`2a995a5d`](https://github.com/Veloera/Veloera/commit/2a995a5d)
- fix gemini panic [`bba61747`](https://github.com/Veloera/Veloera/commit/bba61747)
- 修复数据看板饼图显示错误 [`b91a269d`](https://github.com/Veloera/Veloera/commit/b91a269d)
- 优化数据看板更新逻辑 [`ce05e7dd`](https://github.com/Veloera/Veloera/commit/ce05e7dd)
- 修复兑换码复制带sk- [`79b1201f`](https://github.com/Veloera/Veloera/commit/79b1201f)
- 优化 mj 获取进度 [`fd4ef086`](https://github.com/Veloera/Veloera/commit/fd4ef086)
- add Date header for email [`cdcbebce`](https://github.com/Veloera/Veloera/commit/cdcbebce)
- 如果还有数据，等待一会 [`f712b73c`](https://github.com/Veloera/Veloera/commit/f712b73c)
- 如果还有数据，等待一会 [`ed22a202`](https://github.com/Veloera/Veloera/commit/ed22a202)
- 修复客户端中断请求，计算补全阻塞问题 [`6680f6d8`](https://github.com/Veloera/Veloera/commit/6680f6d8)
- fix postgresql support (#606) [`a398f359`](https://github.com/Veloera/Veloera/commit/a398f359)
- set Accept header if not given (#615) [`57aa637c`](https://github.com/Veloera/Veloera/commit/57aa637c)
- docker compose healthcheck failed (#593) [`64cdb7ea`](https://github.com/Veloera/Veloera/commit/64cdb7ea)
- fix array index not checked (close #588) [`a27a5bcc`](https://github.com/Veloera/Veloera/commit/a27a5bcc)
- fix request count not updated correctly when using batch update [`f073592d`](https://github.com/Veloera/Veloera/commit/f073592d)
- fix url not passing (#562) [`fa41ca98`](https://github.com/Veloera/Veloera/commit/fa41ca98)
- 404 Component is missing (#592) [`e338de45`](https://github.com/Veloera/Veloera/commit/e338de45)
- check user quota when pre-consume quota [`47c08c72`](https://github.com/Veloera/Veloera/commit/47c08c72)
- fix midjourney task update bug [`f7870b1b`](https://github.com/Veloera/Veloera/commit/f7870b1b)
- sum null to 0 (#541) [`8651451e`](https://github.com/Veloera/Veloera/commit/8651451e)
- fix gorm expression [`1c5bb97a`](https://github.com/Veloera/Veloera/commit/1c5bb97a)
- fix gorm expression [`de868e4e`](https://github.com/Veloera/Veloera/commit/de868e4e)
- add default value for base url [`1d258cc8`](https://github.com/Veloera/Veloera/commit/1d258cc8)
- fix random selection is not working when directly using database [`37e09d76`](https://github.com/Veloera/Veloera/commit/37e09d76)
- fix unable to set zero value for base url & model mapping [`159b9e33`](https://github.com/Veloera/Veloera/commit/159b9e33)
- fix priority not updated & random choice not working [`a5647b1e`](https://github.com/Veloera/Veloera/commit/a5647b1e)
- enable cors for token routers and dashboard routers [`946eed4d`](https://github.com/Veloera/Veloera/commit/946eed4d)
- fix url not passing when using custom chat_link [`12ef9679`](https://github.com/Veloera/Veloera/commit/12ef9679)
- only enable cors for relay routers to avoid csrf attack [`25c4c111`](https://github.com/Veloera/Veloera/commit/25c4c111)
- fix oauth2 state not checking [`39ae8075`](https://github.com/Veloera/Veloera/commit/39ae8075)
- fix quota not return when error occurred (close #518) [`01863d3e`](https://github.com/Veloera/Veloera/commit/01863d3e)
- support ali's embedding model (#481, close #469) [`d0a0e871`](https://github.com/Veloera/Veloera/commit/d0a0e871)
- press enter to submit custom model name [`276163af`](https://github.com/Veloera/Veloera/commit/276163af)
- fix log table use created_at as key instead of id [`f0d5e102`](https://github.com/Veloera/Veloera/commit/f0d5e102)
- fix the issue of function_call not working when using model mapping (#462) [`4f2f911e`](https://github.com/Veloera/Veloera/commit/4f2f911e)
- fix error response (close #468) [`a3e267df`](https://github.com/Veloera/Veloera/commit/a3e267df)
- disable channel when 401 received (close #467) [`ac7c0f3a`](https://github.com/Veloera/Veloera/commit/ac7c0f3a)
- fix xunfei crash (#451) [`efeb9a16`](https://github.com/Veloera/Veloera/commit/efeb9a16)
- empty completion issue caused by bad status code from upstream channel (#422) [`05e4f2b4`](https://github.com/Veloera/Veloera/commit/05e4f2b4)
- fix baidu & ali's quota calculation (#444) [`dfaa0183`](https://github.com/Veloera/Veloera/commit/dfaa0183)
- claude model ratio (#449) [`23b1c635`](https://github.com/Veloera/Veloera/commit/23b1c635)
- update cache immediately after cache get [`86c2627c`](https://github.com/Veloera/Veloera/commit/86c2627c)
- fix baidu's embedding api (#398) [`4ef5e202`](https://github.com/Veloera/Veloera/commit/4ef5e202)
- add lock when update quota (close #399) [`eae9b6e6`](https://github.com/Veloera/Veloera/commit/eae9b6e6)
- fix token name too long [`2a527ee4`](https://github.com/Veloera/Veloera/commit/2a527ee4)
- fix finish_reason fileld not fully compatible with OpenAI (close #372, #373) [`476a46ad`](https://github.com/Veloera/Veloera/commit/476a46ad)
- set connection limits for database [`466005de`](https://github.com/Veloera/Veloera/commit/466005de)
- disable eslint when building (close #371, close #376) [`2b088a16`](https://github.com/Veloera/Veloera/commit/2b088a16)
- update tiktoken-go's version to fix resource consumption problem (#392, close #161) [`3a18cebe`](https://github.com/Veloera/Veloera/commit/3a18cebe)
- update no route handler [`3b36608b`](https://github.com/Veloera/Veloera/commit/3b36608b)
- no need to check turnstile when process deletion [`463b0b3c`](https://github.com/Veloera/Veloera/commit/463b0b3c)
- fix stream mode determine related logic (close #360) [`d96cf2e8`](https://github.com/Veloera/Veloera/commit/d96cf2e8)
- calculate usage if not given in non-stream mode (#352) [`446337c3`](https://github.com/Veloera/Veloera/commit/446337c3)
- fix SparkDesk not billed (#344) [`2d49ca6a`](https://github.com/Veloera/Veloera/commit/2d49ca6a)
- fix zhipu streaming (#349) [`c2c455c9`](https://github.com/Veloera/Veloera/commit/c2c455c9)
- fix sse not ending properly in some case [`c9d2e42a`](https://github.com/Veloera/Veloera/commit/c9d2e42a)
- ignore data if not have proper prefix [`8cbbeb78`](https://github.com/Veloera/Veloera/commit/8cbbeb78)
- prompt user that channel test is unavailable [`ec88c0c2`](https://github.com/Veloera/Veloera/commit/ec88c0c2)
- close connection when response ended [`065147b4`](https://github.com/Veloera/Veloera/commit/065147b4)
- fix model mapping cannot be deleted [`f45d5864`](https://github.com/Veloera/Veloera/commit/f45d5864)
- fix model mapping cannot be deleted [`50dec03f`](https://github.com/Veloera/Veloera/commit/50dec03f)
- convert system message to user message for zhipu [`d1b6f492`](https://github.com/Veloera/Veloera/commit/d1b6f492)
- convert system message to user message for claude [`b9f6461d`](https://github.com/Veloera/Veloera/commit/b9f6461d)
- convert system message to user message (close #328) [`0a39521a`](https://github.com/Veloera/Veloera/commit/0a39521a)
- use channel type to determine api type (close #321) [`c134604c`](https://github.com/Veloera/Veloera/commit/c134604c)
- baseURL not working in APITypePaLM (#317) [`929e43ef`](https://github.com/Veloera/Veloera/commit/929e43ef)
- relay router typo for List models (#320) [`dce8bbe1`](https://github.com/Veloera/Veloera/commit/dce8bbe1)
- fix redemption can be used multiple times in some cases [`889af8b2`](https://github.com/Veloera/Veloera/commit/889af8b2)
- fix blank screen after refresh [`4ab3211c`](https://github.com/Veloera/Veloera/commit/4ab3211c)
- fix PaLM2 not billed [`e6938bd2`](https://github.com/Veloera/Veloera/commit/e6938bd2)
- fix model editing now working properly (close #288) [`3c940113`](https://github.com/Veloera/Veloera/commit/3c940113)
- using whitelist when disabling channels (close #292) [`0495b9a0`](https://github.com/Veloera/Veloera/commit/0495b9a0)
- restore display_name/username that deleted before (#268) [`ea73201b`](https://github.com/Veloera/Veloera/commit/ea73201b)
- use transaction to protect redeem process [`d17bdc40`](https://github.com/Veloera/Veloera/commit/d17bdc40)
- fix http2: invalid Connection request header: [\"upgrade\"]" [`991f5bf4`](https://github.com/Veloera/Veloera/commit/991f5bf4)
- fix refresh not working properly (close #229) [`9ec6506c`](https://github.com/Veloera/Veloera/commit/9ec6506c)
- fix cannot enable token if set to unlimited time (close #230) [`f0c40a6c`](https://github.com/Veloera/Veloera/commit/f0c40a6c)
- fix channel search is not working with MySQL [`0cea9e6a`](https://github.com/Veloera/Veloera/commit/0cea9e6a)
- fix AutomaticDisableChannelEnabled option not ignored (close #217) [`bddbf571`](https://github.com/Veloera/Veloera/commit/bddbf571)
- fix the wrong message when channel is deleted [`aabc5466`](https://github.com/Veloera/Veloera/commit/aabc5466)
- fix wrong env var name [`51f19470`](https://github.com/Veloera/Veloera/commit/51f19470)
- update cached user quota after post-consuming (close #204) [`737672fb`](https://github.com/Veloera/Veloera/commit/737672fb)
- InitChannelCache does not filter disabled channels (#201) [`f0dc7f3f`](https://github.com/Veloera/Veloera/commit/f0dc7f3f)
- check if token is nil before using it [`6b1a24d6`](https://github.com/Veloera/Veloera/commit/6b1a24d6)
- do not record if used quota is zero [`57bd907f`](https://github.com/Veloera/Veloera/commit/57bd907f)
- do not charge the user if the amount of tokens used was zero [`dd8e8d5e`](https://github.com/Veloera/Veloera/commit/dd8e8d5e)
- fix usage is not correct [`1ca1aa0c`](https://github.com/Veloera/Veloera/commit/1ca1aa0c)
- fix log sorting (#195) [`f2ba0c03`](https://github.com/Veloera/Veloera/commit/f2ba0c03)
- do not reuse state variable directly [`f5c1fcd3`](https://github.com/Veloera/Veloera/commit/f5c1fcd3)
- reset page number after query [`5fdf670a`](https://github.com/Veloera/Veloera/commit/5fdf670a)
- remove tqdm dependency [`f5564727`](https://github.com/Veloera/Veloera/commit/f5564727)
- fix http status code (close #193) [`8a4cd403`](https://github.com/Veloera/Veloera/commit/8a4cd403)
- disable redis on master node [`28fb4d76`](https://github.com/Veloera/Veloera/commit/28fb4d76)
- fix time_test.sh (#191) [`ca779e4f`](https://github.com/Veloera/Veloera/commit/ca779e4f)
- update time_test.sh [`f51c9824`](https://github.com/Veloera/Veloera/commit/f51c9824)
- only master node can migrate database [`567916bd`](https://github.com/Veloera/Veloera/commit/567916bd)
- fix channel table's sorting problem (#188) [`1f3b3ca7`](https://github.com/Veloera/Veloera/commit/1f3b3ca7)
- do not show dollar balance if not enabled [`6536a7be`](https://github.com/Veloera/Veloera/commit/6536a7be)
- cors allow all headers [`634099e5`](https://github.com/Veloera/Veloera/commit/634099e5)
- fix used amount not correct [`ced9f060`](https://github.com/Veloera/Veloera/commit/ced9f060)
- fix crash when Redis is enabled [`14b85318`](https://github.com/Veloera/Veloera/commit/14b85318)
- fix wrong field name in SQL [`aa0a9f22`](https://github.com/Veloera/Veloera/commit/aa0a9f22)
- do not print response to console [`dc94765d`](https://github.com/Veloera/Veloera/commit/dc94765d)
- fix the shared field is modified [`1cb1f727`](https://github.com/Veloera/Veloera/commit/1cb1f727)
- reduce the table size (close #174) [`bcbfacc0`](https://github.com/Veloera/Veloera/commit/bcbfacc0)
- limit the length of email [`a9099723`](https://github.com/Veloera/Veloera/commit/a9099723)
- fix footer not updated asap [`d79289cc`](https://github.com/Veloera/Veloera/commit/d79289cc)
- do not record completion ratio anymore [`58fb18aa`](https://github.com/Veloera/Veloera/commit/58fb18aa)
- the prompt field can be array type now (close #166, #167) [`630156dc`](https://github.com/Veloera/Veloera/commit/630156dc)
- fix file not committed [`d6dbaff3`](https://github.com/Veloera/Veloera/commit/d6dbaff3)
- now the input field can be array type now (close #149) [`7c7eb6b7`](https://github.com/Veloera/Veloera/commit/7c7eb6b7)
- fix OpenAI-SB balance not correct [`8b2ef666`](https://github.com/Veloera/Veloera/commit/8b2ef666)
- fix group list not correct (close #147) [`955d5f87`](https://github.com/Veloera/Veloera/commit/955d5f87)
- correct OpenAI error code's type [`f97a9ce5`](https://github.com/Veloera/Veloera/commit/f97a9ce5)
- fix not using proxy when update balance [`145bb14c`](https://github.com/Veloera/Veloera/commit/145bb14c)
- fix redemption code's quota not updated [`e0d0674f`](https://github.com/Veloera/Veloera/commit/e0d0674f)
- able to manage root user now [`521ede24`](https://github.com/Veloera/Veloera/commit/521ede24)
- prompt user the feat is not implemented (#125) [`1e1c6a82`](https://github.com/Veloera/Veloera/commit/1e1c6a82)
- fix balance query (close #138) [`98f1a627`](https://github.com/Veloera/Veloera/commit/98f1a627)
- fix balance query (close #138) [`333e4216`](https://github.com/Veloera/Veloera/commit/333e4216)
- add a blank VERSION file (#135) [`7e80e2da`](https://github.com/Veloera/Veloera/commit/7e80e2da)
- update common.RootUserEmail when root's email changed (#132) [`0f6958c5`](https://github.com/Veloera/Veloera/commit/0f6958c5)
- update docker-compose.yml (#131) [`5f045f8c`](https://github.com/Veloera/Veloera/commit/5f045f8c)
- fetch root user's email if blank (#129) [`f19ee053`](https://github.com/Veloera/Veloera/commit/f19ee053)
- fix wrong implementation for /v1/models (close #128) [`fa71daa8`](https://github.com/Veloera/Veloera/commit/fa71daa8)
- add no-cache for index.html [`8b43e0dd`](https://github.com/Veloera/Veloera/commit/8b43e0dd)
- remove no-store for index.html [`92c88fa2`](https://github.com/Veloera/Veloera/commit/92c88fa2)
- do not cache index.html [`38191d55`](https://github.com/Veloera/Veloera/commit/38191d55)
- disable channel with a whitelist [`d9e39f59`](https://github.com/Veloera/Veloera/commit/d9e39f59)
- fix unable to update custom channel's balance [`17b7646c`](https://github.com/Veloera/Veloera/commit/17b7646c)
- show bind options only available (close #65) [`b92ec5e5`](https://github.com/Veloera/Veloera/commit/b92ec5e5)
- use gpt-3.5's encoder if not found (close #110) [`fa79e8b7`](https://github.com/Veloera/Veloera/commit/fa79e8b7)
- make the token number calculation more accurate (#101) [`7c6bf3e9`](https://github.com/Veloera/Veloera/commit/7c6bf3e9)
- add X-Accel-Buffering header on SSE response [`2779d662`](https://github.com/Veloera/Veloera/commit/2779d662)
- fix channel test error checking [`6da34108`](https://github.com/Veloera/Veloera/commit/6da34108)
- handel error response from server correctly (close #90) [`ceb289cb`](https://github.com/Veloera/Veloera/commit/ceb289cb)
- fix error log not recorded (close #83) [`ad01e1f3`](https://github.com/Veloera/Veloera/commit/ad01e1f3)
- fix stream mode checking (#83) [`cc1ef2ff`](https://github.com/Veloera/Veloera/commit/cc1ef2ff)
- update api2d's base url (#83) [`7201bd1c`](https://github.com/Veloera/Veloera/commit/7201bd1c)
- limit the shown text's length (close #80) [`e8da9813`](https://github.com/Veloera/Veloera/commit/e8da9813)
- fix quota not consuming [`8afdc56b`](https://github.com/Veloera/Veloera/commit/8afdc56b)
- fix topup page now showing [`a9ea1d9d`](https://github.com/Veloera/Veloera/commit/a9ea1d9d)
- fix token quota not updated [`ea8e7c51`](https://github.com/Veloera/Veloera/commit/ea8e7c51)
- fix option update logic not working properly [`c48327ff`](https://github.com/Veloera/Veloera/commit/c48327ff)
- fix tab icon & title not changed (close #69) [`a5406c69`](https://github.com/Veloera/Veloera/commit/a5406c69)
- fix lock is not working [`aae92683`](https://github.com/Veloera/Veloera/commit/aae92683)
- remove version suffix for Azure (close #67) [`cc3072c4`](https://github.com/Veloera/Veloera/commit/cc3072c4)
- fix /v1/models not working (close #66) [`bffee4e9`](https://github.com/Veloera/Veloera/commit/bffee4e9)
- fix quota not consumed [`4a0e81fe`](https://github.com/Veloera/Veloera/commit/4a0e81fe)
- fix "[DONE is not valid JSON" (#57) [`246b981e`](https://github.com/Veloera/Veloera/commit/246b981e)
- fix Azure channel not working in stream mode (#57) [`2edd52e8`](https://github.com/Veloera/Veloera/commit/2edd52e8)
- fix SMTPFrom not updated in some cases (close #34) [`e123c66b`](https://github.com/Veloera/Veloera/commit/e123c66b)
- fix garbled email subject (#34) [`9edc82bd`](https://github.com/Veloera/Veloera/commit/9edc82bd)
- support smtp server with port 465 [`46e77389`](https://github.com/Veloera/Veloera/commit/46e77389)
- remove the dot in model name (#57) [`73aa53f5`](https://github.com/Veloera/Veloera/commit/73aa53f5)
- provide a default value for api-version if not given (#57) [`44729da2`](https://github.com/Veloera/Veloera/commit/44729da2)
- handle errors when update option map [`fd19d7d2`](https://github.com/Veloera/Veloera/commit/fd19d7d2)
- fix the default ratio for text-embedding-ada-002 [`9edc54ca`](https://github.com/Veloera/Veloera/commit/9edc54ca)
- the initial quota for new token now calculated correctly (#51) [`e6af636f`](https://github.com/Veloera/Veloera/commit/e6af636f)
- delete a token with a negative quota will now update the account's quota correctly (close #51) [`241ade2f`](https://github.com/Veloera/Veloera/commit/241ade2f)
- return quota to user when delete token (close #37) [`331177d9`](https://github.com/Veloera/Veloera/commit/331177d9)
- shouldn't close c.Request.Body too soon (close #35) [`7c66fc6c`](https://github.com/Veloera/Veloera/commit/7c66fc6c)
- relay bug fix [`aea6c859`](https://github.com/Veloera/Veloera/commit/aea6c859)
- check user's role when manage user (#30) [`7a5057f0`](https://github.com/Veloera/Veloera/commit/7a5057f0)
- check user status when validating token (#23) [`54b1e4ad`](https://github.com/Veloera/Veloera/commit/54b1e4ad)
- root user cannot demote itself now (close #30) [`********`](https://github.com/Veloera/Veloera/commit/********)
- fix MySQL syntax error (#54) [`195e94a7`](https://github.com/Veloera/Veloera/commit/195e94a7)
- specify type for token (close #23) [`5bfc2246`](https://github.com/Veloera/Veloera/commit/5bfc2246)
- remove rate limit for relay api [`fd149c24`](https://github.com/Veloera/Veloera/commit/fd149c24)
- allow all origins (close #20) [`e7a809b0`](https://github.com/Veloera/Veloera/commit/e7a809b0)
- prevent common user from specifying channel id (#12) [`4f8cbd64`](https://github.com/Veloera/Veloera/commit/4f8cbd64)
- allow all origins (close #20) [`1dd92a3f`](https://github.com/Veloera/Veloera/commit/1dd92a3f)
- only reduce remain times when request `/v1/chat/completions` (close #15) [`109736cc`](https://github.com/Veloera/Veloera/commit/109736cc)
- fully support stream mode now (close #3) [`69ee87c5`](https://github.com/Veloera/Veloera/commit/69ee87c5)
- improve the implementation of sse [`b74a17c9`](https://github.com/Veloera/Veloera/commit/b74a17c9)
- fixing SSE support [`5d602e9b`](https://github.com/Veloera/Veloera/commit/5d602e9b)
- update error message to make it more clear (#13) [`01c1b906`](https://github.com/Veloera/Veloera/commit/01c1b906)
- relay more headers [`16271e78`](https://github.com/Veloera/Veloera/commit/16271e78)
- only keep header Authorization & Content-Type [`0198df59`](https://github.com/Veloera/Veloera/commit/0198df59)
- fix http2: invalid Connection request header: ["upgrade"] [`4c0dc50a`](https://github.com/Veloera/Veloera/commit/4c0dc50a)
- copy token to search input if clipboard.writeText is not available (close #6) [`423978ba`](https://github.com/Veloera/Veloera/commit/423978ba)

### Continuous Integration
- Let CI only push to ghcr.io [`1b510d19`](https://github.com/Veloera/Veloera/commit/1b510d19)
- Correct the filename in linux_release [`acf20b08`](https://github.com/Veloera/Veloera/commit/acf20b08)
- Update CI [`c643523f`](https://github.com/Veloera/Veloera/commit/c643523f)
- update ci [`bb313eb2`](https://github.com/Veloera/Veloera/commit/bb313eb2)
- update ci [`2a345ae0`](https://github.com/Veloera/Veloera/commit/2a345ae0)
- update workflows [`1bfc46aa`](https://github.com/Veloera/Veloera/commit/1bfc46aa)
- add stage caching to Dockerfile (#408) [`e4bacc45`](https://github.com/Veloera/Veloera/commit/e4bacc45)
- fix GitHub Actions config [`25017219`](https://github.com/Veloera/Veloera/commit/25017219)
- rename GitHub Actions file [`61dc117d`](https://github.com/Veloera/Veloera/commit/61dc117d)
- ignore alpha version [`1932c56e`](https://github.com/Veloera/Veloera/commit/1932c56e)
- build arm version [`79dc53ff`](https://github.com/Veloera/Veloera/commit/79dc53ff)
- remove arm64 image builder [`570b3bc7`](https://github.com/Veloera/Veloera/commit/570b3bc7)
- remove useless action [`4e8dc8d0`](https://github.com/Veloera/Veloera/commit/4e8dc8d0)

### Performance Improvements
- 运营设置-提示文案 [`470f3a1d`](https://github.com/Veloera/Veloera/commit/470f3a1d)
- 运营设置-数据刷新逻辑 [`65ae7091`](https://github.com/Veloera/Veloera/commit/65ae7091)
- 移除不生效的参数 [`a9d9877b`](https://github.com/Veloera/Veloera/commit/a9d9877b)
- add useEffect and useNavigate hooks to Setting component [`290dcf75`](https://github.com/Veloera/Veloera/commit/290dcf75)
- add tabActiveKey state to Setting component [`aa23c51a`](https://github.com/Veloera/Veloera/commit/aa23c51a)
- 优化其他设置显示效果 [`37113c0e`](https://github.com/Veloera/Veloera/commit/37113c0e)
- code logic [`931d22c9`](https://github.com/Veloera/Veloera/commit/931d22c9)
- prompt when the name of the custom model input already exists [`92c1ed7f`](https://github.com/Veloera/Veloera/commit/92c1ed7f)
- 美化绘画界面UI [`e688e413`](https://github.com/Veloera/Veloera/commit/e688e413)
- 数据看板支持选择时间粒度 [`00306aa1`](https://github.com/Veloera/Veloera/commit/00306aa1)
- UI美化 [`d04d2a6c`](https://github.com/Veloera/Veloera/commit/d04d2a6c)
- 美化数据看板 [`e2317524`](https://github.com/Veloera/Veloera/commit/e2317524)
- 优化数据看板性能 [`1618a8c9`](https://github.com/Veloera/Veloera/commit/1618a8c9)
- 优化数据看板性能和显示效果 [`95e84f2b`](https://github.com/Veloera/Veloera/commit/95e84f2b)
- 美化数据看板 [`8f36a995`](https://github.com/Veloera/Veloera/commit/8f36a995)
- lazy initialization for token encoders (close #566) [`594f06e7`](https://github.com/Veloera/Veloera/commit/594f06e7)
- only return quota when it's not zero [`420c3751`](https://github.com/Veloera/Veloera/commit/420c3751)
- preallocate array capacity [`abbf2fde`](https://github.com/Veloera/Veloera/commit/abbf2fde)
- initialize all token encoder when starting (close #459, close $460) [`fdb2cccf`](https://github.com/Veloera/Veloera/commit/fdb2cccf)
- use a goroutine to handle quota post consumption (#364) [`0e9ff882`](https://github.com/Veloera/Veloera/commit/0e9ff882)
- flush response after response handled (close #364) [`7bddc73b`](https://github.com/Veloera/Veloera/commit/7bddc73b)
- reuse http client to reduce delay [`3da119ef`](https://github.com/Veloera/Veloera/commit/3da119ef)
- validate the request first before send to OpenAI's server [`f6eb4e56`](https://github.com/Veloera/Veloera/commit/f6eb4e56)
- use max_tokens to reduce token consuming [`58fe923c`](https://github.com/Veloera/Veloera/commit/58fe923c)
- load cached about content first (#60) [`57cb1501`](https://github.com/Veloera/Veloera/commit/57cb1501)
- use slice to improve efficiency (#57) [`cf688377`](https://github.com/Veloera/Veloera/commit/cf688377)

### Documentation
- Add whitespace between badges [`c52b112b`](https://github.com/Veloera/Veloera/commit/c52b112b)
- Add cool badges in README [`f0df9d66`](https://github.com/Veloera/Veloera/commit/f0df9d66)
- Add CLAUDE.md [`b5fac30a`](https://github.com/Veloera/Veloera/commit/b5fac30a)
- Sync features section with latest features [`8012db8b`](https://github.com/Veloera/Veloera/commit/8012db8b)
- Add features section at README [`c35392a6`](https://github.com/Veloera/Veloera/commit/c35392a6)
- Replace most new-api with Veloera [`4c03f368`](https://github.com/Veloera/Veloera/commit/4c03f368)
- Update README [`8d02adee`](https://github.com/Veloera/Veloera/commit/8d02adee)
- Change SQLite filename to veloera.db [`9562db63`](https://github.com/Veloera/Veloera/commit/9562db63)
- Rename to Veloera [`e0f83fa7`](https://github.com/Veloera/Veloera/commit/e0f83fa7)
- Put New Api's stuffs to a sepurate folder [`78d8bf29`](https://github.com/Veloera/Veloera/commit/78d8bf29)
- Replace most New API with Gating [`8fdd2c2e`](https://github.com/Veloera/Veloera/commit/8fdd2c2e)
- fix a typo [`be6ffd3c`](https://github.com/Veloera/Veloera/commit/be6ffd3c)
- Update README [`3de5b96c`](https://github.com/Veloera/Veloera/commit/3de5b96c)
- Add proxy usage information note in SystemSetting component [`585c19fc`](https://github.com/Veloera/Veloera/commit/585c19fc)
- Update README with detailed Docker deployment and update instructions [`20723766`](https://github.com/Veloera/Veloera/commit/20723766)
- update README [`1eb706de`](https://github.com/Veloera/Veloera/commit/1eb706de)
- update Midjourney.md [`4a4df758`](https://github.com/Veloera/Veloera/commit/4a4df758)
- update [`2c187fde`](https://github.com/Veloera/Veloera/commit/2c187fde)
- add description for TIKTOKEN_CACHE_DIR (#612) [`22980b4c`](https://github.com/Veloera/Veloera/commit/22980b4c)
- update readme [`fbdea91e`](https://github.com/Veloera/Veloera/commit/fbdea91e)
- update readme [`0f6c132a`](https://github.com/Veloera/Veloera/commit/0f6c132a)
- update readme [`3cac45dc`](https://github.com/Veloera/Veloera/commit/3cac45dc)
- update readme [`197d1d7a`](https://github.com/Veloera/Veloera/commit/197d1d7a)
- update readme (#502) [`215e54fc`](https://github.com/Veloera/Veloera/commit/215e54fc)
- update README [`fe26a144`](https://github.com/Veloera/Veloera/commit/fe26a144)
- update readme [`b57a0eca`](https://github.com/Veloera/Veloera/commit/b57a0eca)
- add QChatGPT (#522) [`1b4cc788`](https://github.com/Veloera/Veloera/commit/1b4cc788)
- update README (close #482) [`c55bb678`](https://github.com/Veloera/Veloera/commit/c55bb678)
- update README (close #482) [`0f949c37`](https://github.com/Veloera/Veloera/commit/0f949c37)
- update README [`9db93316`](https://github.com/Veloera/Veloera/commit/9db93316)
- update README [`ef2c5abb`](https://github.com/Veloera/Veloera/commit/ef2c5abb)
- update README [`49d1a634`](https://github.com/Veloera/Veloera/commit/49d1a634)
- update README [`2a7b8265`](https://github.com/Veloera/Veloera/commit/2a7b8265)
- update README [`8ea7b9aa`](https://github.com/Veloera/Veloera/commit/8ea7b9aa)
- update README [`5136b126`](https://github.com/Veloera/Veloera/commit/5136b126)
- update README [`80a49e01`](https://github.com/Veloera/Veloera/commit/80a49e01)
- update README [`8fb082ba`](https://github.com/Veloera/Veloera/commit/8fb082ba)
- add Japanese README (#425) [`90b4cac7`](https://github.com/Veloera/Veloera/commit/90b4cac7)
- update README [`da1d8199`](https://github.com/Veloera/Veloera/commit/da1d8199)
- update README [`821c559e`](https://github.com/Veloera/Veloera/commit/821c559e)
- update README [`7e2bca7e`](https://github.com/Veloera/Veloera/commit/7e2bca7e)
- update README [`be780462`](https://github.com/Veloera/Veloera/commit/be780462)
- update README (#374) [`f2159e10`](https://github.com/Veloera/Veloera/commit/f2159e10)
- update FastGPT's description (#388) [`29fa94e7`](https://github.com/Veloera/Veloera/commit/29fa94e7)
- update README [`9c436921`](https://github.com/Veloera/Veloera/commit/9c436921)
- update readme (#359) [`89bcaaf9`](https://github.com/Veloera/Veloera/commit/89bcaaf9)
- update deploy-on-sealos (#351) [`afcd1bd2`](https://github.com/Veloera/Veloera/commit/afcd1bd2)
- update README [`30a7f1a1`](https://github.com/Veloera/Veloera/commit/30a7f1a1)
- update README [`d1335ebc`](https://github.com/Veloera/Veloera/commit/d1335ebc)
- update README [`4ec42895`](https://github.com/Veloera/Veloera/commit/4ec42895)
- update README [`3dc5a0f9`](https://github.com/Veloera/Veloera/commit/3dc5a0f9)
- update README [`80a84667`](https://github.com/Veloera/Veloera/commit/80a84667)
- update README [`02da0b51`](https://github.com/Veloera/Veloera/commit/02da0b51)
- update README (close #257) [`6215d2e7`](https://github.com/Veloera/Veloera/commit/6215d2e7)
- update README [`280df277`](https://github.com/Veloera/Veloera/commit/280df277)
- update README [`04f40def`](https://github.com/Veloera/Veloera/commit/04f40def)
- update README [`c48b7bc0`](https://github.com/Veloera/Veloera/commit/c48b7bc0)
- update README [`1ab5fb7d`](https://github.com/Veloera/Veloera/commit/1ab5fb7d)
- update README [`f769711c`](https://github.com/Veloera/Veloera/commit/f769711c)
- update README [`f387cc5e`](https://github.com/Veloera/Veloera/commit/f387cc5e)
- update README [`569b68c4`](https://github.com/Veloera/Veloera/commit/569b68c4)
- update README [`8f6bd51f`](https://github.com/Veloera/Veloera/commit/8f6bd51f)
- update README [`9a16b0f9`](https://github.com/Veloera/Veloera/commit/9a16b0f9)
- update issue templates [`3530309a`](https://github.com/Veloera/Veloera/commit/3530309a)
- update issue templates [`733ebc06`](https://github.com/Veloera/Veloera/commit/733ebc06)
- update issue templates [`6a8567ac`](https://github.com/Veloera/Veloera/commit/6a8567ac)
- update README [`1c82b06f`](https://github.com/Veloera/Veloera/commit/1c82b06f)
- update README [`9e410967`](https://github.com/Veloera/Veloera/commit/9e410967)
- update README [`64c35334`](https://github.com/Veloera/Veloera/commit/64c35334)
- update README (#210) [`0ce572b4`](https://github.com/Veloera/Veloera/commit/0ce572b4)
- update README [`05b0e778`](https://github.com/Veloera/Veloera/commit/05b0e778)
- update README [`f81f4c60`](https://github.com/Veloera/Veloera/commit/f81f4c60)
- update README [`c613d8b6`](https://github.com/Veloera/Veloera/commit/c613d8b6)
- update README [`77a16e64`](https://github.com/Veloera/Veloera/commit/77a16e64)
- update README [`827942c8`](https://github.com/Veloera/Veloera/commit/827942c8)
- update README [`aeb1cad6`](https://github.com/Veloera/Veloera/commit/aeb1cad6)
- update README [`7497f24d`](https://github.com/Veloera/Veloera/commit/7497f24d)
- update README [`97cdb616`](https://github.com/Veloera/Veloera/commit/97cdb616)
- update README [`70cffbc2`](https://github.com/Veloera/Veloera/commit/70cffbc2)
- update README [`868f0474`](https://github.com/Veloera/Veloera/commit/868f0474)
- update README [`42500642`](https://github.com/Veloera/Veloera/commit/42500642)
- update README [`e5ac80c1`](https://github.com/Veloera/Veloera/commit/e5ac80c1)
- update README [`9291b5fb`](https://github.com/Veloera/Veloera/commit/9291b5fb)
- update README [`d97f1df3`](https://github.com/Veloera/Veloera/commit/d97f1df3)
- update README [`f0434c81`](https://github.com/Veloera/Veloera/commit/f0434c81)
- update README [`0e35050b`](https://github.com/Veloera/Veloera/commit/0e35050b)
- update README [`4010164d`](https://github.com/Veloera/Veloera/commit/4010164d)
- update README [`ba89abed`](https://github.com/Veloera/Veloera/commit/ba89abed)
- update issue template [`a680b1b8`](https://github.com/Veloera/Veloera/commit/a680b1b8)
- update README [`b3b7d0a0`](https://github.com/Veloera/Veloera/commit/b3b7d0a0)
- update README (close #175) [`5531e215`](https://github.com/Veloera/Veloera/commit/5531e215)
- update README [`6dcffca0`](https://github.com/Veloera/Veloera/commit/6dcffca0)
- update README [`d754620e`](https://github.com/Veloera/Veloera/commit/d754620e)
- update README [`21111126`](https://github.com/Veloera/Veloera/commit/21111126)
- update README [`d91e7dcf`](https://github.com/Veloera/Veloera/commit/d91e7dcf)
- deploy to Zeabur (#170) [`f89f6c7f`](https://github.com/Veloera/Veloera/commit/f89f6c7f)
- update README [`57b213a0`](https://github.com/Veloera/Veloera/commit/57b213a0)
- update README [`549e944b`](https://github.com/Veloera/Veloera/commit/549e944b)
- update notice [`5f23f59d`](https://github.com/Veloera/Veloera/commit/5f23f59d)
- update issue template (#164) [`538a5d7a`](https://github.com/Veloera/Veloera/commit/538a5d7a)
- update issue template [`07cccdc8`](https://github.com/Veloera/Veloera/commit/07cccdc8)
- update issue template [`f71f0166`](https://github.com/Veloera/Veloera/commit/f71f0166)
- update issue template [`54d7a1c2`](https://github.com/Veloera/Veloera/commit/54d7a1c2)
- update issue template [`f426f31b`](https://github.com/Veloera/Veloera/commit/f426f31b)
- update issue template [`2930577c`](https://github.com/Veloera/Veloera/commit/2930577c)
- add issue templates [`e0951217`](https://github.com/Veloera/Veloera/commit/e0951217)
- update README [`0c34ed4c`](https://github.com/Veloera/Veloera/commit/0c34ed4c)
- update README [`69153e72`](https://github.com/Veloera/Veloera/commit/69153e72)
- update README [`cdef10ca`](https://github.com/Veloera/Veloera/commit/cdef10ca)
- update README [`e398e075`](https://github.com/Veloera/Veloera/commit/e398e075)
- update README [`0b4bf309`](https://github.com/Veloera/Veloera/commit/0b4bf309)
- update README [`c6edb78a`](https://github.com/Veloera/Veloera/commit/c6edb78a)
- update README [`502515bb`](https://github.com/Veloera/Veloera/commit/502515bb)
- update README [`b3839831`](https://github.com/Veloera/Veloera/commit/b3839831)
- update README (#103) [`741c0b9c`](https://github.com/Veloera/Veloera/commit/741c0b9c)
- update README [`481ba41f`](https://github.com/Veloera/Veloera/commit/481ba41f)
- update README (close #97) [`e509899d`](https://github.com/Veloera/Veloera/commit/e509899d)
- update README [`b53cdbaf`](https://github.com/Veloera/Veloera/commit/b53cdbaf)
- fix typo [`09c2e3bc`](https://github.com/Veloera/Veloera/commit/09c2e3bc)
- fix typo [`5cba800f`](https://github.com/Veloera/Veloera/commit/5cba800f)
- update README [`6f8cc712`](https://github.com/Veloera/Veloera/commit/6f8cc712)
- update README [`976c29ea`](https://github.com/Veloera/Veloera/commit/976c29ea)
- update README [`da9ccb52`](https://github.com/Veloera/Veloera/commit/da9ccb52)
- update README (#47) [`8b056bf4`](https://github.com/Veloera/Veloera/commit/8b056bf4)
- add funding link [`e5640857`](https://github.com/Veloera/Veloera/commit/e5640857)
- update LICENSE [`34a3329f`](https://github.com/Veloera/Veloera/commit/34a3329f)
- update README [`1e46b9d1`](https://github.com/Veloera/Veloera/commit/1e46b9d1)
- update Nginx configuration (#3) [`faf84d83`](https://github.com/Veloera/Veloera/commit/faf84d83)
- update Nginx config (close #3) [`eb8f43ac`](https://github.com/Veloera/Veloera/commit/eb8f43ac)
- add help in command line arguments (#14) [`f067f64a`](https://github.com/Veloera/Veloera/commit/f067f64a)
- update deploy tutorial [`f6194fa8`](https://github.com/Veloera/Veloera/commit/f6194fa8)
- fix typo [`abb2449b`](https://github.com/Veloera/Veloera/commit/abb2449b)
- update README [`cc5ef987`](https://github.com/Veloera/Veloera/commit/cc5ef987)
- update Docker start command (close #8) [`9e30524e`](https://github.com/Veloera/Veloera/commit/9e30524e)
- add missing manual deployment documentation (#7) [`336c03a1`](https://github.com/Veloera/Veloera/commit/336c03a1)

### Chore
- Rename to Veloera [`5df1fc6a`](https://github.com/Veloera/Veloera/commit/5df1fc6a)
- Add aider files to gitignore [`56f6c089`](https://github.com/Veloera/Veloera/commit/56f6c089)
- Fix proxy path in Vite config [`fd7265c6`](https://github.com/Veloera/Veloera/commit/fd7265c6)
- Working on new interface [`664017a5`](https://github.com/Veloera/Veloera/commit/664017a5)
- Delete patch files [`4946af08`](https://github.com/Veloera/Veloera/commit/4946af08)
- Make session more stable [`3deccb02`](https://github.com/Veloera/Veloera/commit/3deccb02)
- update .gitignore and docker-compose.yml to include tiktoken_cache directory [`75570af9`](https://github.com/Veloera/Veloera/commit/75570af9)
- Update GitHub Actions workflows and refactor adaptor logic for Docker image builds [`6187656a`](https://github.com/Veloera/Veloera/commit/6187656a)
- Update terminology from "cache ratio" to "cache multiplier" in UI and add placeholder for default create cache ratio [`6d79d899`](https://github.com/Veloera/Veloera/commit/6d79d899)
- Update Azure OpenAI API version and embedding model detection [`1bcf7a3c`](https://github.com/Veloera/Veloera/commit/1bcf7a3c)
- update rerank.md [`c44a32ef`](https://github.com/Veloera/Veloera/commit/c44a32ef)
- update env name and README [`90191b8d`](https://github.com/Veloera/Veloera/commit/90191b8d)
- disable cgo [`cad8a832`](https://github.com/Veloera/Veloera/commit/cad8a832)
- disable cgo [`40d878e8`](https://github.com/Veloera/Veloera/commit/40d878e8)
- replace sqlite lib with prue go lib [`3a2e2244`](https://github.com/Veloera/Veloera/commit/3a2e2244)
- update CI [`13d1b820`](https://github.com/Veloera/Veloera/commit/13d1b820)
- update CI [`1b4fe860`](https://github.com/Veloera/Veloera/commit/1b4fe860)
- update CI [`d10b4700`](https://github.com/Veloera/Veloera/commit/d10b4700)
- 同步deepseek价格 [`34fdac38`](https://github.com/Veloera/Veloera/commit/34fdac38)
- sync gemini aistudio model [`efdd6fb6`](https://github.com/Veloera/Veloera/commit/efdd6fb6)
- update Node.js version in CI workflows from 16 to 18 [`a652ac61`](https://github.com/Veloera/Veloera/commit/a652ac61)
- add ffmpeg-tools to Dockerfile for enhanced multimedia processing [`e9256760`](https://github.com/Veloera/Veloera/commit/e9256760)
- change workflow runners to self-hosted for Docker and release jobs [`82ae6e4e`](https://github.com/Veloera/Veloera/commit/82ae6e4e)
- update language in index.html to Chinese [`d75ecfc6`](https://github.com/Veloera/Veloera/commit/d75ecfc6)
- 更新gemini模型 [`f3a71197`](https://github.com/Veloera/Veloera/commit/f3a71197)
- 更新gemini模型 [`3fa1d93b`](https://github.com/Veloera/Veloera/commit/3fa1d93b)
- Update dependencies and refactor JSON handling #614 [`79de02b0`](https://github.com/Veloera/Veloera/commit/79de02b0)
- update default STREAMING_TIMEOUT [`7b3394d8`](https://github.com/Veloera/Veloera/commit/7b3394d8)
- Update docker-compose.yml [`9e087097`](https://github.com/Veloera/Veloera/commit/9e087097)
- go.mod [`e8286e47`](https://github.com/Veloera/Veloera/commit/e8286e47)
- update .env.example [`b012505f`](https://github.com/Veloera/Veloera/commit/b012505f)
- update .env.example [`c7c870d4`](https://github.com/Veloera/Veloera/commit/c7c870d4)
- 删除无用日志 [`0a80231e`](https://github.com/Veloera/Veloera/commit/0a80231e)
- update model ratio [`fe0ed128`](https://github.com/Veloera/Veloera/commit/fe0ed128)
- 更新令牌分组描述 [`50eab6b4`](https://github.com/Veloera/Veloera/commit/50eab6b4)
- 令牌分组描述歧义 [`2e734e0c`](https://github.com/Veloera/Veloera/commit/2e734e0c)
- update footer [`be54369c`](https://github.com/Veloera/Veloera/commit/be54369c)
- remove useless code [`0c46d0c7`](https://github.com/Veloera/Veloera/commit/0c46d0c7)
- update @so1ve/prettier-config to version 3.1.0 [`d6f6403f`](https://github.com/Veloera/Veloera/commit/d6f6403f)
- indent recovery [`c152b4de`](https://github.com/Veloera/Veloera/commit/c152b4de)
- epay [`a0a3807b`](https://github.com/Veloera/Veloera/commit/a0a3807b)
- 优化自动禁用代码 [`5acf0745`](https://github.com/Veloera/Veloera/commit/5acf0745)
- 优化relay代码 [`dd12a005`](https://github.com/Veloera/Veloera/commit/dd12a005)
- 优化relay代码 [`fbe6cd75`](https://github.com/Veloera/Veloera/commit/fbe6cd75)
- 优化relay代码 [`8a9ff36f`](https://github.com/Veloera/Veloera/commit/8a9ff36f)
- remove useless code [`a6b6bcfe`](https://github.com/Veloera/Veloera/commit/a6b6bcfe)
- update token page [`07e55cc9`](https://github.com/Veloera/Veloera/commit/07e55cc9)
- gopool [`e84300f4`](https://github.com/Veloera/Veloera/commit/e84300f4)
- log format [`fae918c0`](https://github.com/Veloera/Veloera/commit/fae918c0)
- update model radio [`963985e7`](https://github.com/Veloera/Veloera/commit/963985e7)
- mj [`a3880d55`](https://github.com/Veloera/Veloera/commit/a3880d55)
- openai stream [`e262a9bd`](https://github.com/Veloera/Veloera/commit/e262a9bd)
- remove useless code [`bd9bf4b7`](https://github.com/Veloera/Veloera/commit/bd9bf4b7)
- 重构 [`d767ae04`](https://github.com/Veloera/Veloera/commit/d767ae04)
- Add Anthropic claude-3-5-sonnet-20240620 to model list [`6b07e6fb`](https://github.com/Veloera/Veloera/commit/6b07e6fb)
- 添加注释 [`d8602896`](https://github.com/Veloera/Veloera/commit/d8602896)
- 删除无用代码 [`d05a786b`](https://github.com/Veloera/Veloera/commit/d05a786b)
- 删除无用代码 [`01160658`](https://github.com/Veloera/Veloera/commit/01160658)
- update minimax url [`98a99130`](https://github.com/Veloera/Veloera/commit/98a99130)
- token counter [`a3de3091`](https://github.com/Veloera/Veloera/commit/a3de3091)
- update model list [`09512167`](https://github.com/Veloera/Veloera/commit/09512167)
- update InitTokenEncoders (#255) [`a3b3e6cc`](https://github.com/Veloera/Veloera/commit/a3b3e6cc)
- update tiktoken (#254) [`9dcec277`](https://github.com/Veloera/Veloera/commit/9dcec277)
- 删除无用代码 [`21839ed1`](https://github.com/Veloera/Veloera/commit/21839ed1)
- update model ratio [`86b17fcc`](https://github.com/Veloera/Veloera/commit/86b17fcc)
- 移除无用代码 [`408c2bdd`](https://github.com/Veloera/Veloera/commit/408c2bdd)
- 优化按次计费的数据库查询次数 [`f3f36daf`](https://github.com/Veloera/Veloera/commit/f3f36daf)
- update model ratio [`f1fb7b32`](https://github.com/Veloera/Veloera/commit/f1fb7b32)
- lint fix [`962dc984`](https://github.com/Veloera/Veloera/commit/962dc984)
- delete useless file [`951383c3`](https://github.com/Veloera/Veloera/commit/951383c3)
- delete useless dir [`87b62100`](https://github.com/Veloera/Veloera/commit/87b62100)
- Chunking Strategy [`06c86397`](https://github.com/Veloera/Veloera/commit/06c86397)
- update dependency [`2d2fec24`](https://github.com/Veloera/Veloera/commit/2d2fec24)
- reformat code [`d34b55c1`](https://github.com/Veloera/Veloera/commit/d34b55c1)
- drop idx_channels_key on start [`c3a01dec`](https://github.com/Veloera/Veloera/commit/c3a01dec)
- update README.md [`d2de6755`](https://github.com/Veloera/Veloera/commit/d2de6755)
- check before get userGroup [`ccaf64bb`](https://github.com/Veloera/Veloera/commit/ccaf64bb)
- change use_time to int [`705171e4`](https://github.com/Veloera/Veloera/commit/705171e4)
- remove unused import [`e485bc76`](https://github.com/Veloera/Veloera/commit/e485bc76)
- 数据看板限制用户只能查询跨度一个月的数据 [`e41fcd56`](https://github.com/Veloera/Veloera/commit/e41fcd56)
- UpdateMidjourneyTaskBulk with SafeGoroutine [`d30b9321`](https://github.com/Veloera/Veloera/commit/d30b9321)
- add SafeGoroutine [`febcadb4`](https://github.com/Veloera/Veloera/commit/febcadb4)
- cache username [`f07b9f8a`](https://github.com/Veloera/Veloera/commit/f07b9f8a)
- delete model price log [`fb95216b`](https://github.com/Veloera/Veloera/commit/fb95216b)
- show prompt to let the user know [`fbe9985f`](https://github.com/Veloera/Veloera/commit/fbe9985f)
- bump golang.org/x/net from 0.10.0 to 0.17.0 (#591) [`114587b4`](https://github.com/Veloera/Veloera/commit/114587b4)
- delete 360's 360GPT_S2_V9.4 [`d663de3e`](https://github.com/Veloera/Veloera/commit/d663de3e)
- delete 360's 360GPT_S2_V9.4 [`a85ecace`](https://github.com/Veloera/Veloera/commit/a85ecace)
- add database migration prompt [`cbd62011`](https://github.com/Veloera/Veloera/commit/cbd62011)
- sync database indices with pro version [`4701897e`](https://github.com/Veloera/Veloera/commit/4701897e)
- sync model schema [`53b2cace`](https://github.com/Veloera/Veloera/commit/53b2cace)
- remind user to change default password (close #516) [`f0fc991b`](https://github.com/Veloera/Veloera/commit/f0fc991b)
- add MEMORY_CACHE_ENABLED env variable [`f9b748c2`](https://github.com/Veloera/Veloera/commit/f9b748c2)
- update ali's model name [`fd984636`](https://github.com/Veloera/Veloera/commit/fd984636)
- update channel test prompt [`0d50ad4b`](https://github.com/Veloera/Veloera/commit/0d50ad4b)
- update error code [`959bcdef`](https://github.com/Veloera/Veloera/commit/959bcdef)
- add error prompt for Azure [`a721a5b6`](https://github.com/Veloera/Veloera/commit/a721a5b6)
- pass through error out [`621eb91b`](https://github.com/Veloera/Veloera/commit/621eb91b)
- update variable name [`3da12e99`](https://github.com/Veloera/Veloera/commit/3da12e99)
- use unknown as placeholder [`e42119b7`](https://github.com/Veloera/Veloera/commit/e42119b7)
- resolve conflicts [`1e16ef3e`](https://github.com/Veloera/Veloera/commit/1e16ef3e)
- update prompt [`150d068e`](https://github.com/Veloera/Veloera/commit/150d068e)
- update i18n [`c3d85a28`](https://github.com/Veloera/Veloera/commit/c3d85a28)
- update prompt [`7422b0d0`](https://github.com/Veloera/Veloera/commit/7422b0d0)
- update domain [`b464e290`](https://github.com/Veloera/Veloera/commit/b464e290)
- update title for xunfei [`b7d0616a`](https://github.com/Veloera/Veloera/commit/b7d0616a)
- update prompt for xunfei [`ce9c8024`](https://github.com/Veloera/Veloera/commit/ce9c8024)
- update i18n [`3e81d8af`](https://github.com/Veloera/Veloera/commit/3e81d8af)
- adjust ui [`b8cb86c2`](https://github.com/Veloera/Veloera/commit/b8cb86c2)
- automatically add related models when switch type [`f31d400b`](https://github.com/Veloera/Veloera/commit/f31d400b)
- send a notice to user indicating that the current channel type does not support testing [`bc2f48b1`](https://github.com/Veloera/Veloera/commit/bc2f48b1)
- do not hardcode cache time (close #302) [`4eea0966`](https://github.com/Veloera/Veloera/commit/4eea0966)
- optimize frontend (#293) [`9b4d1964`](https://github.com/Veloera/Veloera/commit/9b4d1964)
- update prompts of channel config page [`806bf824`](https://github.com/Veloera/Veloera/commit/806bf824)
- adjust channel config page [`ce93c9b6`](https://github.com/Veloera/Veloera/commit/ce93c9b6)
- add more context in error message [`c87e05bf`](https://github.com/Veloera/Veloera/commit/c87e05bf)
- add notice for baidu [`fcc1e2d5`](https://github.com/Veloera/Veloera/commit/fcc1e2d5)
- make subscription api compatible with official api [`4139a703`](https://github.com/Veloera/Veloera/commit/4139a703)
- update i18n (#262) [`2b17bb8d`](https://github.com/Veloera/Veloera/commit/2b17bb8d)
- remove email in UsersTable to make room for other fields (#246) [`3bab5b48`](https://github.com/Veloera/Veloera/commit/3bab5b48)
- update channel add & edit page [`f3bccee3`](https://github.com/Veloera/Veloera/commit/f3bccee3)
- add model parameter to the time_test script (#245) [`d84b0b0f`](https://github.com/Veloera/Veloera/commit/d84b0b0f)
- update countTokenMessages (#238) [`b09daf5e`](https://github.com/Veloera/Veloera/commit/b09daf5e)
- set the default token quota to 1$ [`c90c0ece`](https://github.com/Veloera/Veloera/commit/c90c0ece)
- ignore FRONTEND_BASE_URL on master node [`edc51566`](https://github.com/Veloera/Veloera/commit/edc51566)
- more hints in model mapping textarea (#205) [`a326ac4b`](https://github.com/Veloera/Veloera/commit/a326ac4b)
- show the HTTP status code in the test_time script to determine the success or failure of the request (#200) [`99fed1f8`](https://github.com/Veloera/Veloera/commit/99fed1f8)
- do not show completion ratio anymore [`4dc5388a`](https://github.com/Veloera/Veloera/commit/4dc5388a)
- update default ratio for text-embedding-ada-002 [`7adac1c0`](https://github.com/Veloera/Veloera/commit/7adac1c0)
- show equivalent amount next to remaining quota in the user editing page (#198) [`6f051283`](https://github.com/Veloera/Veloera/commit/6f051283)
- update the number that representing the unlimited quota [`4a6a7f46`](https://github.com/Veloera/Veloera/commit/4a6a7f46)
- billing api now will return a large number if unlimited quota is set [`94ba3dd0`](https://github.com/Veloera/Veloera/commit/94ba3dd0)
- adjust table width [`a515f928`](https://github.com/Veloera/Veloera/commit/a515f928)
- update en.json [`b0bfb9c9`](https://github.com/Veloera/Veloera/commit/b0bfb9c9)
- update completion ratio for GPT-3 [`3aff61a9`](https://github.com/Veloera/Veloera/commit/3aff61a9)
- update translations [`0fd1ff4d`](https://github.com/Veloera/Veloera/commit/0fd1ff4d)
- update translations [`e2777bf7`](https://github.com/Veloera/Veloera/commit/e2777bf7)
- fix translation [`604ff205`](https://github.com/Veloera/Veloera/commit/604ff205)
- update en.json [`2dd4ad0e`](https://github.com/Veloera/Veloera/commit/2dd4ad0e)
- use notice to show password (#107) [`d4869dfa`](https://github.com/Veloera/Veloera/commit/d4869dfa)
- able to clear all models now [`d0c454c7`](https://github.com/Veloera/Veloera/commit/d0c454c7)
- update base url setting [`fe135fd5`](https://github.com/Veloera/Veloera/commit/fe135fd5)
- use NODE_TYPE to determine node type [`b090e50f`](https://github.com/Veloera/Veloera/commit/b090e50f)
- update time_test.sh [`36e681e8`](https://github.com/Veloera/Veloera/commit/36e681e8)
- add time_test.sh [`75cd522c`](https://github.com/Veloera/Veloera/commit/75cd522c)
- update docker-compose.yml (#189) [`c893d046`](https://github.com/Veloera/Veloera/commit/c893d046)
- update one-api.service [`c6717307`](https://github.com/Veloera/Veloera/commit/c6717307)
- update docker-compose.yml [`76a39131`](https://github.com/Veloera/Veloera/commit/76a39131)
- format logs [`00151a01`](https://github.com/Veloera/Veloera/commit/00151a01)
- print more logs [`b86de464`](https://github.com/Veloera/Veloera/commit/b86de464)
- update api message [`dc7bb78c`](https://github.com/Veloera/Veloera/commit/dc7bb78c)
- update api message [`853a2880`](https://github.com/Veloera/Veloera/commit/853a2880)
- update prompt [`1b5c628e`](https://github.com/Veloera/Veloera/commit/1b5c628e)
- update description [`868d9a87`](https://github.com/Veloera/Veloera/commit/868d9a87)
- make the user unable to search token by id [`5c18c559`](https://github.com/Veloera/Veloera/commit/5c18c559)
- hide token id for user [`72ea805f`](https://github.com/Veloera/Veloera/commit/72ea805f)
- set the fallback model ratio to 30 [`24a4b323`](https://github.com/Veloera/Veloera/commit/24a4b323)
- update prompt [`8e805e23`](https://github.com/Veloera/Veloera/commit/8e805e23)
- update api endpoint for CloseAI [`eb70b846`](https://github.com/Veloera/Veloera/commit/eb70b846)
- add x-requested-with header in CORS setting [`6855d0dc`](https://github.com/Veloera/Veloera/commit/6855d0dc)
- remove -0613 suffix for Azure (#163) [`e87ad1f4`](https://github.com/Veloera/Veloera/commit/e87ad1f4)
- update gpt3.5 completion ratio [`38668e73`](https://github.com/Veloera/Veloera/commit/38668e73)
- add trailing slash for API calling [`39481eb6`](https://github.com/Veloera/Veloera/commit/39481eb6)
- record ratio detail in log [`07785341`](https://github.com/Veloera/Veloera/commit/07785341)
- update prompt [`9d0bec83`](https://github.com/Veloera/Veloera/commit/9d0bec83)
- add more log types [`d29c2730`](https://github.com/Veloera/Veloera/commit/d29c2730)
- update test logic [`9301b3fe`](https://github.com/Veloera/Veloera/commit/9301b3fe)
- bump github.com/gin-gonic/gin from 1.9.0 to 1.9.1 (#134) [`139624b8`](https://github.com/Veloera/Veloera/commit/139624b8)
- update config prompt (close #133) [`2f44aaa6`](https://github.com/Veloera/Veloera/commit/2f44aaa6)
- make channel test related code separated [`54215dc3`](https://github.com/Veloera/Veloera/commit/54215dc3)
- only check OpenAI channel & custom channel [`f9f42997`](https://github.com/Veloera/Veloera/commit/f9f42997)
- prompt user if redemption code not input [`1cc7c201`](https://github.com/Veloera/Veloera/commit/1cc7c201)
- set initial quota for root user [`a3a1b612`](https://github.com/Veloera/Veloera/commit/a3a1b612)
- set default value for Azure's api version if not set [`ef9dca28`](https://github.com/Veloera/Veloera/commit/ef9dca28)
- rewrite 429 prompt text (close #96) [`ced89398`](https://github.com/Veloera/Veloera/commit/ced89398)
- update input label [`519cb030`](https://github.com/Veloera/Veloera/commit/519cb030)
- update prompt [`d1e9b86f`](https://github.com/Veloera/Veloera/commit/d1e9b86f)
- only show two digits [`68e53d3e`](https://github.com/Veloera/Veloera/commit/68e53d3e)
- fix typo [`8835d830`](https://github.com/Veloera/Veloera/commit/8835d830)
- update prompt for Azure channel configuration (#57) [`74c1ba7c`](https://github.com/Veloera/Veloera/commit/74c1ba7c)
- update placeholder text (#36) [`4fed003f`](https://github.com/Veloera/Veloera/commit/4fed003f)
- update placeholder text (#36) [`a1ea1bf6`](https://github.com/Veloera/Veloera/commit/a1ea1bf6)
- update gitignore [`4fb07b6d`](https://github.com/Veloera/Veloera/commit/4fb07b6d)
- update Dockerfile [`8be7c9ae`](https://github.com/Veloera/Veloera/commit/8be7c9ae)
- update dependency [`b8747840`](https://github.com/Veloera/Veloera/commit/b8747840)

### Style
- Format code using gofmt [`270a37f4`](https://github.com/Veloera/Veloera/commit/270a37f4)
- format code [`6b79b89d`](https://github.com/Veloera/Veloera/commit/6b79b89d)
- Enhance LogsTable header tags with improved styling and visual hierarchy [`3ed50787`](https://github.com/Veloera/Veloera/commit/3ed50787)
- fix UI related problems [`25eab0b2`](https://github.com/Veloera/Veloera/commit/25eab0b2)
- add positive attribute to submit buttons (close #113) [`34bce5b4`](https://github.com/Veloera/Veloera/commit/34bce5b4)
- add comma to quota stat [`2eee97e9`](https://github.com/Veloera/Veloera/commit/2eee97e9)
- hide scroll bar [`6167e20b`](https://github.com/Veloera/Veloera/commit/6167e20b)
- add bottom margin for unlimited times button [`c76027a2`](https://github.com/Veloera/Veloera/commit/c76027a2)

### Refactor
- remove unused mutex from RelayInfo struct [`27266208`](https://github.com/Veloera/Veloera/commit/27266208)
- move maxFileSize variable inside GetFileBase64FromUrl function [`8a233207`](https://github.com/Veloera/Veloera/commit/8a233207)
- 把common/instants.go里的从Getenv获取的参数，放到init.go的LoadEnv函数里获取 把constant/env.go里的从Getenv获取的参数，放到env.go的InitEnv函数里获取。以避免.env文件配置参数不起作用的情况 [`86f6bb7a`](https://github.com/Veloera/Veloera/commit/86f6bb7a)
- Remove duplicate model settings initialization in main function [`f89d8a0f`](https://github.com/Veloera/Veloera/commit/f89d8a0f)
- Update localization keys for API address in English translations and adjust related UI labels [`3d6d1990`](https://github.com/Veloera/Veloera/commit/3d6d1990)
- systemSetting component to enhance UI structure and add new configuration options [`09adc6f2`](https://github.com/Veloera/Veloera/commit/09adc6f2)
- use handleFieldChange function on change event [`af7f886c`](https://github.com/Veloera/Veloera/commit/af7f886c)
- Improve token quota consumption logic [`dd82618c`](https://github.com/Veloera/Veloera/commit/dd82618c)
- Update ClaudeResponse error handling to use pointer for ClaudeError and improve nil checks in response processing [`8c7c3955`](https://github.com/Veloera/Veloera/commit/8c7c3955)
- Enhance Claude response handling [`53b35998`](https://github.com/Veloera/Veloera/commit/53b35998)
- Enhance error handling in AWS and Claude response processing by updating function signatures and improving error propagation [`ee302c06`](https://github.com/Veloera/Veloera/commit/ee302c06)
- Streamline AWS and Claude response handling by consolidating logic and improving error management [`2c81a5f0`](https://github.com/Veloera/Veloera/commit/2c81a5f0)
- Replace direct access to ImageUrl with GetImageMedia method across multiple relay channels [`c183c123`](https://github.com/Veloera/Veloera/commit/c183c123)
- Change ClaudeError field type to non-pointer and enhance response handling with reasoning content [`19bfa158`](https://github.com/Veloera/Veloera/commit/19bfa158)
- Simplify OpenAI handler function signature and remove unused TextResponseWithError struct; introduce common_handler for rerank functionality [`69e44a03`](https://github.com/Veloera/Veloera/commit/69e44a03)
- Update token usage calculation in FormatClaudeResponseInfo #865 [`1644dbc8`](https://github.com/Veloera/Veloera/commit/1644dbc8)
- Update OIDC status check to use oidc_enabled flag [`0033f5ba`](https://github.com/Veloera/Veloera/commit/0033f5ba)
- Remove OIDC configuration from option initialization [`e52ac52e`](https://github.com/Veloera/Veloera/commit/e52ac52e)
- Migrate OIDC configuration to system settings [`66682584`](https://github.com/Veloera/Veloera/commit/66682584)
- Improve responsive design across multiple setting pages [`94549f96`](https://github.com/Veloera/Veloera/commit/94549f96)
- Remove unnecessary transition styles and simplify sidebar state management [`627f95b0`](https://github.com/Veloera/Veloera/commit/627f95b0)
- Improve sidebar state management and layout responsiveness [`8b99eec4`](https://github.com/Veloera/Veloera/commit/8b99eec4)
- Improve mobile responsiveness and scrolling behavior in UI layout [`b2938ffe`](https://github.com/Veloera/Veloera/commit/b2938ffe)
- Enhance UI layout and styling with responsive design improvements [`d9cf0885`](https://github.com/Veloera/Veloera/commit/d9cf0885)
- Make Channel Setting nullable and improve setting handling #836 [`97d948cd`](https://github.com/Veloera/Veloera/commit/97d948cd)
- Improve price rendering with clearer token and price calculations [`db01994c`](https://github.com/Veloera/Veloera/commit/db01994c)
- Update topup amount type from int to int64 for improved precision [`5a10ebd3`](https://github.com/Veloera/Veloera/commit/5a10ebd3)
- Simplify chat menu items rendering in SiderBar [`867187ab`](https://github.com/Veloera/Veloera/commit/867187ab)
- Improve quota calculation precision using floating-point arithmetic [`bb848b2f`](https://github.com/Veloera/Veloera/commit/bb848b2f)
- Remove redundant user quota retrieval in audio relay [`81137e05`](https://github.com/Veloera/Veloera/commit/81137e05)
- Reorganize sidebar navigation and add personal settings route [`fd22948e`](https://github.com/Veloera/Veloera/commit/fd22948e)
- Improve model request rate limit middleware execution [`b841ce00`](https://github.com/Veloera/Veloera/commit/b841ce00)
- Centralize stream handling and helper functions in relay package [`37a83ecc`](https://github.com/Veloera/Veloera/commit/37a83ecc)
- Extract operation-related settings into a separate package [`98b27a17`](https://github.com/Veloera/Veloera/commit/98b27a17)
- Add index to Username column in Log model [`7208a65e`](https://github.com/Veloera/Veloera/commit/7208a65e)
- Update rate limit configuration to use dynamic expiration duration [`4084b180`](https://github.com/Veloera/Veloera/commit/4084b180)
- Improve channel testing and model price handling [`d042a1bd`](https://github.com/Veloera/Veloera/commit/d042a1bd)
- Reorganize Claude MaxTokens configuration UI layout [`ae5b874a`](https://github.com/Veloera/Veloera/commit/ae5b874a)
- Enhance user context and quota management [`069f2672`](https://github.com/Veloera/Veloera/commit/069f2672)
- Improve message content parsing with robust type handling [`cc5066c5`](https://github.com/Veloera/Veloera/commit/cc5066c5)
- Improve message content handling and quota error responses [`b9b69b01`](https://github.com/Veloera/Veloera/commit/b9b69b01)
- Optimize sensitive word detection and text processing [`1f4f9123`](https://github.com/Veloera/Veloera/commit/1f4f9123)
- Improve quota error messages with formatted quota display [`2d42145b`](https://github.com/Veloera/Veloera/commit/2d42145b)
- Simplify model mapping and pricing logic across relay modules [`06da65a9`](https://github.com/Veloera/Veloera/commit/06da65a9)
- Replace manual goroutine creation with gopool.Go [`5937d850`](https://github.com/Veloera/Veloera/commit/5937d850)
- Optimize user caching and token retrieval methods [`b1847509`](https://github.com/Veloera/Veloera/commit/b1847509)
- Simplify root user notification and remove global email variable [`0907a078`](https://github.com/Veloera/Veloera/commit/0907a078)
- Improve CompletionRatio handling with thread-safe access and initialization [`9d9c461c`](https://github.com/Veloera/Veloera/commit/9d9c461c)
- Optimize channel testing and model menu generation (fix #761) [`3239c605`](https://github.com/Veloera/Veloera/commit/3239c605)
- Improve channel property update mechanism (fix #761) [`e6f4587f`](https://github.com/Veloera/Veloera/commit/e6f4587f)
- Optimize log retrieval with separate channel name fetching (fix #751) [`bc62d1bb`](https://github.com/Veloera/Veloera/commit/bc62d1bb)
- Optimize Dockerfile for Go build process [`81591f20`](https://github.com/Veloera/Veloera/commit/81591f20)
- improve SSE response handling in Playground [`39aacf5f`](https://github.com/Veloera/Veloera/commit/39aacf5f)
- update log queries to explicitly reference 'logs' table for clarity and consistency [`1c5740d5`](https://github.com/Veloera/Veloera/commit/1c5740d5)
- access_token auth [`006bc372`](https://github.com/Veloera/Veloera/commit/006bc372)
- realtime log render [`f1e3cd6f`](https://github.com/Veloera/Veloera/commit/f1e3cd6f)
- realtime i18n [`f417a109`](https://github.com/Veloera/Veloera/commit/f417a109)
- realtime quota [`99245e4c`](https://github.com/Veloera/Veloera/commit/99245e4c)
- update group handling and rendering logic [`d13d81ba`](https://github.com/Veloera/Veloera/commit/d13d81ba)
- token cache logic [`bb5e032d`](https://github.com/Veloera/Veloera/commit/bb5e032d)
- remove redundant group column handling in user queries [`ca8b7ed1`](https://github.com/Veloera/Veloera/commit/ca8b7ed1)
- user cache logic [`ed435e5c`](https://github.com/Veloera/Veloera/commit/ed435e5c)
- Playground controller [`c8614f98`](https://github.com/Veloera/Veloera/commit/c8614f98)
- streamline log processing by introducing formatUserLogs function [`10d896aa`](https://github.com/Veloera/Veloera/commit/10d896aa)
- enhance log retrieval and user interaction in LogsTable component [`118eb362`](https://github.com/Veloera/Veloera/commit/118eb362)
- improve user group handling and add GetUserUsableGroups function [`77861e64`](https://github.com/Veloera/Veloera/commit/77861e64)
- migrate group ratio and user usable groups logic to new setting package [`4fc1fe31`](https://github.com/Veloera/Veloera/commit/4fc1fe31)
- Remove unused context and logging in CovertGemini2OpenAI function [`241c9389`](https://github.com/Veloera/Veloera/commit/241c9389)
- Update Message methods to use pointer receivers [`c4e256e6`](https://github.com/Veloera/Veloera/commit/c4e256e6)
- Update SetToolCalls method to use pointer receiver [`87a5e40d`](https://github.com/Veloera/Veloera/commit/87a5e40d)
- Update OpenAI request and message handling [`0c326556`](https://github.com/Veloera/Veloera/commit/0c326556)
- Simplify Gemini function parameter handling [`656e8092`](https://github.com/Veloera/Veloera/commit/656e8092)
- Enhance error handling in Gemini request conversion [`03256dbd`](https://github.com/Veloera/Veloera/commit/03256dbd)
- Improve channel status update logic and clean up code [`2d865eb7`](https://github.com/Veloera/Veloera/commit/2d865eb7)
- Update SystemInstructions type in GeminiChatRequest and adjust handling in CovertGemini2OpenAI [`41637137`](https://github.com/Veloera/Veloera/commit/41637137)
- Remove unused translation function calls in LogsTable component [`68b7e0e9`](https://github.com/Veloera/Veloera/commit/68b7e0e9)
- Simplify average calculations in Detail component [`263547eb`](https://github.com/Veloera/Veloera/commit/263547eb)
- Simplify PersonalSetting component layout [`77b8d918`](https://github.com/Veloera/Veloera/commit/77b8d918)
- improve validation logic and error handling in relay-text.go [`a9f739a7`](https://github.com/Veloera/Veloera/commit/a9f739a7)
- realtime log [`d5966992`](https://github.com/Veloera/Veloera/commit/d5966992)
- 调整配置文件，优化注释和变量命名 [`2e18d5f9`](https://github.com/Veloera/Veloera/commit/2e18d5f9)
- image relay [`b0d5491a`](https://github.com/Veloera/Veloera/commit/b0d5491a)
- audio relay [`bcc7f3ed`](https://github.com/Veloera/Veloera/commit/bcc7f3ed)
- 重构流模式逻辑 [`70290658`](https://github.com/Veloera/Veloera/commit/70290658)
- 移除已废弃模型 [`7e262382`](https://github.com/Veloera/Veloera/commit/7e262382)
- 重构敏感词 [`bfbbe67f`](https://github.com/Veloera/Veloera/commit/bfbbe67f)
- 运营设置-数据刷新 [`98c347e0`](https://github.com/Veloera/Veloera/commit/98c347e0)
- 运营设置-倍率设置 [`b283365e`](https://github.com/Veloera/Veloera/commit/b283365e)
- 运营设置-额度设置 [`968ef1e5`](https://github.com/Veloera/Veloera/commit/968ef1e5)
- 运营设置-监控设置 [`88bc2958`](https://github.com/Veloera/Veloera/commit/88bc2958)
- 运营设置-数据看板设置 [`76f6b41b`](https://github.com/Veloera/Veloera/commit/76f6b41b)
- 运营设置-日志设置 [`003745ab`](https://github.com/Veloera/Veloera/commit/003745ab)
- 运营设置-屏蔽词过滤设置 [`96468ce6`](https://github.com/Veloera/Veloera/commit/96468ce6)
- 运营设置-绘图设置 [`9886cdd5`](https://github.com/Veloera/Veloera/commit/9886cdd5)
- 运营设置-通用设置 [`83dd6298`](https://github.com/Veloera/Veloera/commit/83dd6298)
- 重构计费代码 [`783e8fd7`](https://github.com/Veloera/Veloera/commit/783e8fd7)
- dark mode [`e9abe5b7`](https://github.com/Veloera/Veloera/commit/e9abe5b7)
- renderGroup function [`3e90b6d5`](https://github.com/Veloera/Veloera/commit/3e90b6d5)
- 代码结构优化 [`03195771`](https://github.com/Veloera/Veloera/commit/03195771)
- 修改超时时间 [`84e05446`](https://github.com/Veloera/Veloera/commit/84e05446)
- remove consumeQuota [`9b2e5c29`](https://github.com/Veloera/Veloera/commit/9b2e5c29)
- change UI, improve interaction [`6413bf34`](https://github.com/Veloera/Veloera/commit/6413bf34)
- update logging related logic [`42451d9d`](https://github.com/Veloera/Veloera/commit/42451d9d)
- add set event stream headers func (#402) [`ca512f6a`](https://github.com/Veloera/Veloera/commit/ca512f6a)
- update billing related code [`fe8f216d`](https://github.com/Veloera/Veloera/commit/fe8f216d)
- refactor openai related code [`12a0e710`](https://github.com/Veloera/Veloera/commit/12a0e710)
- refactor claude related code [`e628b643`](https://github.com/Veloera/Veloera/commit/e628b643)
- refactor claude related code [`675847bf`](https://github.com/Veloera/Veloera/commit/675847bf)
- do not use redis to store session [`431d505f`](https://github.com/Veloera/Veloera/commit/431d505f)
- rename function [`33846ce4`](https://github.com/Veloera/Veloera/commit/33846ce4)
- split the relay controller [`f6fe3467`](https://github.com/Veloera/Veloera/commit/f6fe3467)
- make operation settings separated from system settings [`75545a1f`](https://github.com/Veloera/Veloera/commit/75545a1f)
- enable model configuration on default group (close #143) [`813bf0bd`](https://github.com/Veloera/Veloera/commit/813bf0bd)
- bind quota to account instead of token (close #64, #31) [`01abed0a`](https://github.com/Veloera/Veloera/commit/01abed0a)
- rename file to conform to standards [`5798fdac`](https://github.com/Veloera/Veloera/commit/5798fdac)
- use built in smtp library (close #34) [`3710688e`](https://github.com/Veloera/Veloera/commit/3710688e)
- use tiktoken-go to calculate token number [`b08cd7e1`](https://github.com/Veloera/Veloera/commit/b08cd7e1)
- improve relay's implementation [`23ec541b`](https://github.com/Veloera/Veloera/commit/23ec541b)
- use quota instead of times [`601fa5ce`](https://github.com/Veloera/Veloera/commit/601fa5ce)

### Other
- format: Format code use gofmt [`********`](https://github.com/Veloera/Veloera/commit/********)
- Fix workflow badge path in README [`123fc658`](https://github.com/Veloera/Veloera/commit/123fc658)
- Simplify CI [`e030c702`](https://github.com/Veloera/Veloera/commit/e030c702)
- format: Format code [`125396b2`](https://github.com/Veloera/Veloera/commit/125396b2)
- revert: 9872cd86 [`7a0604f4`](https://github.com/Veloera/Veloera/commit/7a0604f4)
- revert: Revert changes in commit f258225 [`cf60bb1d`](https://github.com/Veloera/Veloera/commit/cf60bb1d)
- feature: Allow user to use multiple api keys in one channel. [`0be85ebf`](https://github.com/Veloera/Veloera/commit/0be85ebf)
- feature: Add gift code [`e6b906b8`](https://github.com/Veloera/Veloera/commit/e6b906b8)
- 🐛fix: Fix the issue where new whitelist email domain names cannot be added in the system settings [`995c19a9`](https://github.com/Veloera/Veloera/commit/995c19a9)
- Merge pull request #927 from QuentinHsu/refactor-system-setting [`71d0d759`](https://github.com/Veloera/Veloera/commit/71d0d759)
- Merge pull request #967 from neotf/fix-01 [`d283f6b3`](https://github.com/Veloera/Veloera/commit/d283f6b3)
- Merge pull request #959 from Praying/main [`909c5eb2`](https://github.com/Veloera/Veloera/commit/909c5eb2)
- Merge pull request #953 from wkxu/main [`8efa12b9`](https://github.com/Veloera/Veloera/commit/8efa12b9)
- Merge pull request #956 from HynoR/feat/xai [`7b997b3a`](https://github.com/Veloera/Veloera/commit/7b997b3a)
- Merge pull request #944 from lamcodes/main [`c4c1099a`](https://github.com/Veloera/Veloera/commit/c4c1099a)
- Update: Gemini channel fetch_models [`524d4a65`](https://github.com/Veloera/Veloera/commit/524d4a65)
- Merge pull request #930 from Yiffyi/main [`67cbbc22`](https://github.com/Veloera/Veloera/commit/67cbbc22)
- Update MaxTokens for gemini model to 300 in test request [`79b35e38`](https://github.com/Veloera/Veloera/commit/79b35e38)
- Merge pull request #936 from lamcodes/main [`03e8ab41`](https://github.com/Veloera/Veloera/commit/03e8ab41)
- Set MaxTokens to 50 for gemini [`30f32c6a`](https://github.com/Veloera/Veloera/commit/30f32c6a)
- Update model-ratio.go [`f6e88874`](https://github.com/Veloera/Veloera/commit/f6e88874)
- Update model-ratio.go [`a29f4d88`](https://github.com/Veloera/Veloera/commit/a29f4d88)
- Update model-ratio.go [`424424c1`](https://github.com/Veloera/Veloera/commit/424424c1)
- Merge remote-tracking branch 'origin/main' [`42a2418d`](https://github.com/Veloera/Veloera/commit/42a2418d)
- Update README.md [`5cb317bd`](https://github.com/Veloera/Veloera/commit/5cb317bd)
- Merge pull request #925 from Calcium-Ion/setup [`37dd1ef0`](https://github.com/Veloera/Veloera/commit/37dd1ef0)
- Merge remote-tracking branch 'origin/main' [`552e2850`](https://github.com/Veloera/Veloera/commit/552e2850)
- Merge pull request #909 from jasinliu/feature/fix-dify-thinking [`1dc2284d`](https://github.com/Veloera/Veloera/commit/1dc2284d)
- Merge pull request #893 from wizcas/replace-linux-do-icon [`f4cc90c8`](https://github.com/Veloera/Veloera/commit/f4cc90c8)
- Merge pull request #895 from Feiyuyu0503/main [`140d3a97`](https://github.com/Veloera/Veloera/commit/140d3a97)
- Merge pull request #912 from OrdinarySF/main [`2ecb742e`](https://github.com/Veloera/Veloera/commit/2ecb742e)
- Merge pull request #914 from JoeyLearnsToCode/main [`9066cfa8`](https://github.com/Veloera/Veloera/commit/9066cfa8)
- Merge pull request #916 from xifan2333/fix/systemSettingsUI [`4f437f30`](https://github.com/Veloera/Veloera/commit/4f437f30)
- fix dify thinking [`dc132655`](https://github.com/Veloera/Veloera/commit/dc132655)
- update model ratio [`3516aad3`](https://github.com/Veloera/Veloera/commit/3516aad3)
- update model ratio [`1df39e5a`](https://github.com/Veloera/Veloera/commit/1df39e5a)
- replace the linuxdo icon in the login form [`a9522075`](https://github.com/Veloera/Veloera/commit/a9522075)
- Merge pull request #886 from seefs001/main [`983d31bf`](https://github.com/Veloera/Veloera/commit/983d31bf)
- Merge pull request #872 from neotf/main [`b5aa3c12`](https://github.com/Veloera/Veloera/commit/b5aa3c12)
- Update README [`962e803d`](https://github.com/Veloera/Veloera/commit/962e803d)
- Update README [`ff57ced2`](https://github.com/Veloera/Veloera/commit/ff57ced2)
- Update README [`2223806c`](https://github.com/Veloera/Veloera/commit/2223806c)
- Merge remote-tracking branch 'origin/main' [`a4a40c49`](https://github.com/Veloera/Veloera/commit/a4a40c49)
- Merge pull request #851 from HynoR/main [`5a67bdf1`](https://github.com/Veloera/Veloera/commit/5a67bdf1)
- Merge pull request #874 from HynoR/feat/gemini2 [`b84b6aff`](https://github.com/Veloera/Veloera/commit/b84b6aff)
- Sync Cohere Latest Model [`e9884904`](https://github.com/Veloera/Veloera/commit/e9884904)
- Merge branch 'main' into main [`8e68bcce`](https://github.com/Veloera/Veloera/commit/8e68bcce)
- Merge pull request #867 from Sh1n3zZ/wrong-think-label-fix [`a3811634`](https://github.com/Veloera/Veloera/commit/a3811634)
- Merge pull request #857 from asjfoajs/main [`e5b6aa6e`](https://github.com/Veloera/Veloera/commit/e5b6aa6e)
- Refactor: Optimize the ImageHandler under the Alibaba large model to retrieve the key from the header. Reason: The info parameter already includes the key, so there is no need to retrieve it again from the header. Solution: Delete the code for obtaining the key and directly use info.ApiKey. [`23596d22`](https://github.com/Veloera/Veloera/commit/23596d22)
- Update README.md [`c25d4d8d`](https://github.com/Veloera/Veloera/commit/c25d4d8d)
- Update README.md [`b291fbff`](https://github.com/Veloera/Veloera/commit/b291fbff)
- Update README.md [`e68edf81`](https://github.com/Veloera/Veloera/commit/e68edf81)
- Merge pull request #854 from seefs001/main [`5ff16f9b`](https://github.com/Veloera/Veloera/commit/5ff16f9b)
- Merge pull request #855 from Calcium-Ion/claude [`f614cfa5`](https://github.com/Veloera/Veloera/commit/f614cfa5)
- fix panic [`2048b451`](https://github.com/Veloera/Veloera/commit/2048b451)
- Merge remote-tracking branch 'origin/main' [`5059cbdb`](https://github.com/Veloera/Veloera/commit/5059cbdb)
- Fix Deepseek Cache Ratio [`f7852ada`](https://github.com/Veloera/Veloera/commit/f7852ada)
- Merge pull request #848 from wzxjohn/feature/oidc [`495bbcb6`](https://github.com/Veloera/Veloera/commit/495bbcb6)
- Merge remote-tracking branch 'origin/main' [`8de29fbb`](https://github.com/Veloera/Veloera/commit/8de29fbb)
- Merge pull request #849 from OrdinarySF/main [`f2163acf`](https://github.com/Veloera/Veloera/commit/f2163acf)
- Merge remote-tracking branch 'origin/main' [`bbe7223a`](https://github.com/Veloera/Veloera/commit/bbe7223a)
- Merge pull request #845 from Sh1n3zZ/gemini-embedding [`ecb5b563`](https://github.com/Veloera/Veloera/commit/ecb5b563)
- Merge remote-tracking branch 'origin/main' [`69db1f14`](https://github.com/Veloera/Veloera/commit/69db1f14)
- Merge pull request #842 from asjfoajs/dev [`c7e1bab1`](https://github.com/Veloera/Veloera/commit/c7e1bab1)
- Fix: Under Ali's large model, the task ID result for image retrieval is incorrect. Reason: The URL is incomplete, missing baseurl. Solution: Add baseurl. url := fmt.Sprintf("%s/api/v1/tasks/%s", info.BaseUrl, taskID). [`434e9d76`](https://github.com/Veloera/Veloera/commit/434e9d76)
- Merge pull request #830 from Calcium-Ion/decimal [`a0ca3eff`](https://github.com/Veloera/Veloera/commit/a0ca3eff)
- Merge pull request #828 from Calcium-Ion/ui [`3352bacd`](https://github.com/Veloera/Veloera/commit/3352bacd)
- Merge pull request #826 from Calcium-Ion/cache [`d9390ff4`](https://github.com/Veloera/Veloera/commit/d9390ff4)
- Merge pull request #821 from Calcium-Ion/cache [`618908f6`](https://github.com/Veloera/Veloera/commit/618908f6)
- Merge pull request #820 from Calcium-Ion/cache [`7c03ad71`](https://github.com/Veloera/Veloera/commit/7c03ad71)
- Merge pull request #815 from Sh1n3zZ/openrouter-adapter [`b9b66dda`](https://github.com/Veloera/Veloera/commit/b9b66dda)
- Update README.md [`37bb34b4`](https://github.com/Veloera/Veloera/commit/37bb34b4)
- Update README.md [`7855f83e`](https://github.com/Veloera/Veloera/commit/7855f83e)
- Merge remote-tracking branch 'origin/main' [`a42c3b62`](https://github.com/Veloera/Veloera/commit/a42c3b62)
- Merge pull request #805 from PaperPlaneDeemo/main [`be228ccd`](https://github.com/Veloera/Veloera/commit/be228ccd)
- Merge branch 'Calcium-Ion:main' into main [`14848ff7`](https://github.com/Veloera/Veloera/commit/14848ff7)
- fix [`15295049`](https://github.com/Veloera/Veloera/commit/15295049)
- init openrouter adaptor [`19a318c9`](https://github.com/Veloera/Veloera/commit/19a318c9)
- Merge pull request #788 from MartialBE/main [`5b9e2756`](https://github.com/Veloera/Veloera/commit/5b9e2756)
- Merge branch 'main' into thinking [`607e3206`](https://github.com/Veloera/Veloera/commit/607e3206)
- Merge pull request #781 from zeyugao/main [`dc36fded`](https://github.com/Veloera/Veloera/commit/dc36fded)
- Merge pull request #783 from Calcium-Ion/rate-limit [`3017882f`](https://github.com/Veloera/Veloera/commit/3017882f)
- Pass extra_body to the backend [`af00f7b3`](https://github.com/Veloera/Veloera/commit/af00f7b3)
- fix typo [`6c7a8c81`](https://github.com/Veloera/Veloera/commit/6c7a8c81)
- Merge pull request #778 from utopeadia/main [`4b6101b3`](https://github.com/Veloera/Veloera/commit/4b6101b3)
- 进一步美化刷新图标 [`c541d6c9`](https://github.com/Veloera/Veloera/commit/c541d6c9)
- 优化日志刷新图标显示 [`7dfcd135`](https://github.com/Veloera/Veloera/commit/7dfcd135)
- Merge pull request #775 from Calcium-Ion/model_mappping [`8dd4ce98`](https://github.com/Veloera/Veloera/commit/8dd4ce98)
- Merge pull request #773 from wellcoming/patch-1 [`6e0046f7`](https://github.com/Veloera/Veloera/commit/6e0046f7)
- Merge pull request #770 from Calcium-Ion/refactor_notify [`2b743550`](https://github.com/Veloera/Veloera/commit/2b743550)
- Merge pull request #768 from lgphone/main [`63f34123`](https://github.com/Veloera/Veloera/commit/63f34123)
- Update .env.example [`a13bea5f`](https://github.com/Veloera/Veloera/commit/a13bea5f)
- Merge pull request #763 from Sh1n3zZ/support-imagen-3.0-generate-002 [`2e3b920a`](https://github.com/Veloera/Veloera/commit/2e3b920a)
- Merge remote-tracking branch 'origin/main' [`995b3a24`](https://github.com/Veloera/Veloera/commit/995b3a24)
- Merge pull request #735 from jyc001/main [`78f19d46`](https://github.com/Veloera/Veloera/commit/78f19d46)
- Merge pull request #759 from nightcoffee/patch-1 [`814be845`](https://github.com/Veloera/Veloera/commit/814be845)
- Merge pull request #714 from NitroRCr/main [`81d11e5d`](https://github.com/Veloera/Veloera/commit/81d11e5d)
- Merge pull request #723 from kuwork/main [`88bdedd2`](https://github.com/Veloera/Veloera/commit/88bdedd2)
- update CI [`7fce084a`](https://github.com/Veloera/Veloera/commit/7fce084a)
- Merge pull request #746 from zjjxwhh/main [`0e89939a`](https://github.com/Veloera/Veloera/commit/0e89939a)
- Merge remote-tracking branch 'origin/main' [`2c2d1da2`](https://github.com/Veloera/Veloera/commit/2c2d1da2)
- Merge pull request #736 from xy3xy3/main [`ec50f665`](https://github.com/Veloera/Veloera/commit/ec50f665)
- Merge pull request #742 from HynoR/chore/ds [`1a09b1ae`](https://github.com/Veloera/Veloera/commit/1a09b1ae)
- 更正硅基流动的SenseVoiceSmall模型名字 [`8910efb1`](https://github.com/Veloera/Veloera/commit/8910efb1)
- Merge pull request #2 from jyc001/dev [`206dbfa4`](https://github.com/Veloera/Veloera/commit/206dbfa4)
- Merge pull request #1 from jyc001/dev [`68bd7f70`](https://github.com/Veloera/Veloera/commit/68bd7f70)
- feat add FIM support for siliconflow [`ce426995`](https://github.com/Veloera/Veloera/commit/ce426995)
- Merge pull request #727 from HynoR/feat/autogemini [`562c6633`](https://github.com/Veloera/Veloera/commit/562c6633)
- Merge branch 'main' into main [`89d48a66`](https://github.com/Veloera/Veloera/commit/89d48a66)
- f*** o3-mini [`b80c1ee3`](https://github.com/Veloera/Veloera/commit/b80c1ee3)
- Merge remote-tracking branch 'origin/main' [`030ffd5f`](https://github.com/Veloera/Veloera/commit/030ffd5f)
- Merge pull request #694 from yinuan-i/main [`fec448eb`](https://github.com/Veloera/Veloera/commit/fec448eb)
- Merge pull request #715 from seefs001/main [`dd593e1a`](https://github.com/Veloera/Veloera/commit/dd593e1a)
- Merge remote-tracking branch 'origin/main' [`ced9c6e5`](https://github.com/Veloera/Veloera/commit/ced9c6e5)
- Merge branch 'Calcium-Ion:main' into main [`344d8046`](https://github.com/Veloera/Veloera/commit/344d8046)
- Merge pull request #713 from seefs001/main [`5a4319b9`](https://github.com/Veloera/Veloera/commit/5a4319b9)
- Fix M3E not working [`7588c42b`](https://github.com/Veloera/Veloera/commit/7588c42b)
- Merge pull request #710 from hubutui/main [`6cc9c36a`](https://github.com/Veloera/Veloera/commit/6cc9c36a)
- fix : chanel test did not refresh [`8a2d220c`](https://github.com/Veloera/Veloera/commit/8a2d220c)
- Merge branch 'Calcium-Ion:main' into main [`0b3a0064`](https://github.com/Veloera/Veloera/commit/0b3a0064)
- Support for MokaAI M3E [`126f04e0`](https://github.com/Veloera/Veloera/commit/126f04e0)
- CI: update workflows [`53a941a6`](https://github.com/Veloera/Veloera/commit/53a941a6)
- Fix temperature not being set to 0 due to json omitempty [`eda7ef50`](https://github.com/Veloera/Veloera/commit/eda7ef50)
- Merge pull request #705 from maranello-o/main [`7f8112a3`](https://github.com/Veloera/Veloera/commit/7f8112a3)
- Merge pull request #699 from detecti1/feat/show-log-with-channel-name [`055e77e4`](https://github.com/Veloera/Veloera/commit/055e77e4)
- Merge pull request #709 from HynoR/feat/update-ratio [`a2872dec`](https://github.com/Veloera/Veloera/commit/a2872dec)
- Merge branch 'Calcium-Ion:main' into main [`ad051fc2`](https://github.com/Veloera/Veloera/commit/ad051fc2)
- Fix JSON parsing error when record.other is empty string [`f7277933`](https://github.com/Veloera/Veloera/commit/f7277933)
- Add channel name (tooltip / detail) to logs [`94fa2810`](https://github.com/Veloera/Veloera/commit/94fa2810)
- Update IP restriction messages for clarity in English localization and placeholder text in EditToken component. Enhanced user guidance by specifying that leaving the IP field blank means no restrictions. [`5387d7f4`](https://github.com/Veloera/Veloera/commit/5387d7f4)
- Merge pull request #693 from Calcium-Ion/refactor-auth [`b85a07e5`](https://github.com/Veloera/Veloera/commit/b85a07e5)
- Adjust streaming timeout for OpenAI models in OaiStreamHandler [`8518ca65`](https://github.com/Veloera/Veloera/commit/8518ca65)
- Update Dockerfile to use Bun for package management and build process [`cd192e27`](https://github.com/Veloera/Veloera/commit/cd192e27)
- Enhance user search functionality to support ID and keyword searches. Updated query conditions to allow searching by user ID alongside username, email, and display name. Improved handling of numeric and string keywords in search queries. [`80fcd4e9`](https://github.com/Veloera/Veloera/commit/80fcd4e9)
- Merge pull request #692 from Calcium-Ion/fix-channel-model-length [`3f8c12c1`](https://github.com/Veloera/Veloera/commit/3f8c12c1)
- revert cache.go [`08a89a50`](https://github.com/Veloera/Veloera/commit/08a89a50)
- Fix model name length validation limit [`4cf9d078`](https://github.com/Veloera/Veloera/commit/4cf9d078)
- 4fa7fefe [`4fa7fefe`](https://github.com/Veloera/Veloera/commit/4fa7fefe)
- Fix channel model length issue [`239bc469`](https://github.com/Veloera/Veloera/commit/239bc469)
- Merge pull request #689 from iszcz/new512 [`b5de003e`](https://github.com/Veloera/Veloera/commit/b5de003e)
- Update model-ratio.go [`8ede1bf1`](https://github.com/Veloera/Veloera/commit/8ede1bf1)
- Merge pull request #686 from delph1s/main [`ba1aad8a`](https://github.com/Veloera/Veloera/commit/ba1aad8a)
- Update README.md [`bf9a492f`](https://github.com/Veloera/Veloera/commit/bf9a492f)
- Merge pull request #683 from iszcz/new512 [`16725d12`](https://github.com/Veloera/Veloera/commit/16725d12)
- Update channel-test.go [`687f07bc`](https://github.com/Veloera/Veloera/commit/687f07bc)
- fix redis [`b4f17543`](https://github.com/Veloera/Veloera/commit/b4f17543)
- Merge pull request #679 from kingxjs/main [`65af1a4d`](https://github.com/Veloera/Veloera/commit/65af1a4d)
- Merge pull request #677 from mageia/master [`1ae0a3fb`](https://github.com/Veloera/Veloera/commit/1ae0a3fb)
- Merge branch 'main' [`fe2e8f1a`](https://github.com/Veloera/Veloera/commit/fe2e8f1a)
- Merge pull request #680 from Calcium-Ion/refactor_redis [`a5f7f8af`](https://github.com/Veloera/Veloera/commit/a5f7f8af)
- 使用原生document构建input再次尝试复制命令 [`e4f9787c`](https://github.com/Veloera/Veloera/commit/e4f9787c)
- 修复 PostgreSQL 中用户组查询错误 [`304c92ce`](https://github.com/Veloera/Veloera/commit/304c92ce)
- Merge pull request #676 from Calcium-Ion/refactor_redis [`05874dcc`](https://github.com/Veloera/Veloera/commit/05874dcc)
- Merge pull request #674 from Yan-Zero/main [`a1b864bc`](https://github.com/Veloera/Veloera/commit/a1b864bc)
- Merge pull request #673 from Yan-Zero/main [`91b777f3`](https://github.com/Veloera/Veloera/commit/91b777f3)
- Merge pull request #672 from Yan-Zero/main [`458dd1bd`](https://github.com/Veloera/Veloera/commit/458dd1bd)
- fix #663 [`52c023a1`](https://github.com/Veloera/Veloera/commit/52c023a1)
- update dockerignore [`5f082d72`](https://github.com/Veloera/Veloera/commit/5f082d72)
- update model ratio [`0b1354ed`](https://github.com/Veloera/Veloera/commit/0b1354ed)
- Merge pull request #661 from tenacioustommy/fix-title-schema [`132c7139`](https://github.com/Veloera/Veloera/commit/132c7139)
- Merge pull request #662 from xqx333/main [`bb3deb7b`](https://github.com/Veloera/Veloera/commit/bb3deb7b)
- Update relay-text.go [`c86762b6`](https://github.com/Veloera/Veloera/commit/c86762b6)
- fix delete title schema [`3409d7a6`](https://github.com/Veloera/Veloera/commit/3409d7a6)
- fix typo [`b3576f24`](https://github.com/Veloera/Veloera/commit/b3576f24)
- Merge pull request #656 from Yan-Zero/main [`aaf5cece`](https://github.com/Veloera/Veloera/commit/aaf5cece)
- Merge branch 'Calcium-Ion:main' into main [`a8a2195a`](https://github.com/Veloera/Veloera/commit/a8a2195a)
- Merge remote-tracking branch 'origin/main' [`be0c240e`](https://github.com/Veloera/Veloera/commit/be0c240e)
- Merge pull request #652 from Yan-Zero/main [`61495a46`](https://github.com/Veloera/Veloera/commit/61495a46)
- Merge remote-tracking branch 'origin/main' [`cf3287a1`](https://github.com/Veloera/Veloera/commit/cf3287a1)
- Merge pull request #651 from tenacioustommy/fix-gemini-json [`eec8f523`](https://github.com/Veloera/Veloera/commit/eec8f523)
- fix-gemini-json [`cce2990d`](https://github.com/Veloera/Veloera/commit/cce2990d)
- Merge pull request #648 from palboss/main [`794f6a6e`](https://github.com/Veloera/Veloera/commit/794f6a6e)
- Update user.go [`a02bc334`](https://github.com/Veloera/Veloera/commit/a02bc334)
- Update user.go [`f54d0cb3`](https://github.com/Veloera/Veloera/commit/f54d0cb3)
- Merge branch 'feat/o1' [`865b98a4`](https://github.com/Veloera/Veloera/commit/865b98a4)
- Merge pull request #645 from MartialBE/gemini_res_format [`5bdbf3a6`](https://github.com/Veloera/Veloera/commit/5bdbf3a6)
- Merge remote-tracking branch 'origin/feat/o1' into feat/o1 [`eac34634`](https://github.com/Veloera/Veloera/commit/eac34634)
- Merge branch 'Calcium-Ion:main' into feat/o1 [`3b58b498`](https://github.com/Veloera/Veloera/commit/3b58b498)
- Merge pull request #636 from HynoR/fix/smfix [`c289694f`](https://github.com/Veloera/Veloera/commit/c289694f)
- Merge pull request #641 from HynoR/main [`c37319cb`](https://github.com/Veloera/Veloera/commit/c37319cb)
- Merge pull request #642 from MartialBE/fix_gemini_thinking [`57f03674`](https://github.com/Veloera/Veloera/commit/57f03674)
- Merge branch 'Calcium-Ion:main' into fix/smfix [`5e3ba457`](https://github.com/Veloera/Veloera/commit/5e3ba457)
- Merge pull request #631 from xqx333/main [`70e78608`](https://github.com/Veloera/Veloera/commit/70e78608)
- Update channel.go [`1f4fc2d5`](https://github.com/Veloera/Veloera/commit/1f4fc2d5)
- Merge pull request #630 from xqx333/main [`c76021e9`](https://github.com/Veloera/Veloera/commit/c76021e9)
- Update channel.go [`173c9bc6`](https://github.com/Veloera/Veloera/commit/173c9bc6)
- Update cache.go [`739c0cc3`](https://github.com/Veloera/Veloera/commit/739c0cc3)
- Merge pull request #628 from Calcium-Ion/pr482-merge [`1f70904f`](https://github.com/Veloera/Veloera/commit/1f70904f)
- Merge remote-tracking branch 'guoruqiang/main' into pr482-merge [`c8f437c1`](https://github.com/Veloera/Veloera/commit/c8f437c1)
- Update README.md [`aab3887d`](https://github.com/Veloera/Veloera/commit/aab3887d)
- Merge pull request #625 from QAbot-zh/fix/bindwechat [`25ab1d40`](https://github.com/Veloera/Veloera/commit/25ab1d40)
- fix bindWeChat tips [`6c995d4b`](https://github.com/Veloera/Veloera/commit/6c995d4b)
- Merge pull request #624 from Calcium-Ion/channel-setting [`fb9f50ce`](https://github.com/Veloera/Veloera/commit/fb9f50ce)
- Merge pull request #623 from Calcium-Ion/channel-setting [`c1a5b0e1`](https://github.com/Veloera/Veloera/commit/c1a5b0e1)
- Merge pull request #618 from HynoR/feat/modeledit [`6ca68651`](https://github.com/Veloera/Veloera/commit/6ca68651)
- Merge pull request #617 from kingxjs/main [`e2f6444b`](https://github.com/Veloera/Veloera/commit/e2f6444b)
- Merge pull request #622 from Calcium-Ion/i18n-fix [`8e2b6d0a`](https://github.com/Veloera/Veloera/commit/8e2b6d0a)
- Update README [`cfdf6e48`](https://github.com/Veloera/Veloera/commit/cfdf6e48)
- Update README.en.md [`84130cdf`](https://github.com/Veloera/Veloera/commit/84130cdf)
- Add README.en.md [`d54cab2a`](https://github.com/Veloera/Veloera/commit/d54cab2a)
- Update README.md [`7aa8b88c`](https://github.com/Veloera/Veloera/commit/7aa8b88c)
- Merge branch 'Calcium-Ion:main' into feat/modeledit [`2eddb934`](https://github.com/Veloera/Veloera/commit/2eddb934)
- Update EditChannel.js [`36bf4b39`](https://github.com/Veloera/Veloera/commit/36bf4b39)
- Merge pull request #616 from Calcium-Ion/panel [`430d5fcd`](https://github.com/Veloera/Veloera/commit/430d5fcd)
- Merge pull request #613 from Calcium-Ion/mobile [`0455f30d`](https://github.com/Veloera/Veloera/commit/0455f30d)
- Merge pull request #612 from Calcium-Ion/mobile [`21d4dcad`](https://github.com/Veloera/Veloera/commit/21d4dcad)
- Merge pull request #611 from Calcium-Ion/mobile [`db84b26e`](https://github.com/Veloera/Veloera/commit/db84b26e)
- Merge pull request #610 from Calcium-Ion/mobile [`69f57728`](https://github.com/Veloera/Veloera/commit/69f57728)
- Merge pull request #609 from Calcium-Ion/mobile [`e5dc21d5`](https://github.com/Veloera/Veloera/commit/e5dc21d5)
- Merge pull request #605 from jochne/patch-1 [`1e1a22e7`](https://github.com/Veloera/Veloera/commit/1e1a22e7)
- Update relay-xunfei.go [`70b5a7fd`](https://github.com/Veloera/Veloera/commit/70b5a7fd)
- Merge pull request #600 from wzxjohn/upstream [`904a1858`](https://github.com/Veloera/Veloera/commit/904a1858)
- Merge pull request #597 from daggeryu/patch-3 [`3eca5809`](https://github.com/Veloera/Veloera/commit/3eca5809)
- fix 关键词搜索加标签聚合时，大于1个空标签渠道无法展开的问题 [`b9007ced`](https://github.com/Veloera/Veloera/commit/b9007ced)
- Merge pull request #596 from daggeryu/patch-2 [`8c42ea19`](https://github.com/Veloera/Veloera/commit/8c42ea19)
- 删除无用代码 [`9ce8940d`](https://github.com/Veloera/Veloera/commit/9ce8940d)
- Update README.md [`a7b5d684`](https://github.com/Veloera/Veloera/commit/a7b5d684)
- Update go.mod [`45bf4961`](https://github.com/Veloera/Veloera/commit/45bf4961)
- Merge pull request #594 from Calcium-Ion/gzip [`c645bf7e`](https://github.com/Veloera/Veloera/commit/c645bf7e)
- Merge pull request #593 from Calcium-Ion/gzip [`6b2f6753`](https://github.com/Veloera/Veloera/commit/6b2f6753)
- Merge pull request #592 from Calcium-Ion/gzip [`c75bc956`](https://github.com/Veloera/Veloera/commit/c75bc956)
- Merge pull request #591 from Calcium-Ion/no-cache [`4937a6d1`](https://github.com/Veloera/Veloera/commit/4937a6d1)
- Merge pull request #590 from iszcz/new512 [`ee990419`](https://github.com/Veloera/Veloera/commit/ee990419)
- 1 [`c8a29251`](https://github.com/Veloera/Veloera/commit/c8a29251)
- Update docker-compose.yml [`07b1c9a4`](https://github.com/Veloera/Veloera/commit/07b1c9a4)
- Merge pull request #589 from mrhaoji/main [`5d8de46e`](https://github.com/Veloera/Veloera/commit/5d8de46e)
- Merge pull request #588 from iszcz/new512 [`f693c13c`](https://github.com/Veloera/Veloera/commit/f693c13c)
- Update EditTagModal.js [`89cd0db2`](https://github.com/Veloera/Veloera/commit/89cd0db2)
- Update README.md [`ae57dd7b`](https://github.com/Veloera/Veloera/commit/ae57dd7b)
- Merge remote-tracking branch 'origin/main' [`87d763e6`](https://github.com/Veloera/Veloera/commit/87d763e6)
- Merge pull request #587 from Calcium-Ion/channel-tag [`08f3562e`](https://github.com/Veloera/Veloera/commit/08f3562e)
- Update docker-compose.yml [`2d1b2676`](https://github.com/Veloera/Veloera/commit/2d1b2676)
- Update README.md [`1035a8e0`](https://github.com/Veloera/Veloera/commit/1035a8e0)
- Merge pull request #586 from Calcium-Ion/channel-tag [`ea433b2e`](https://github.com/Veloera/Veloera/commit/ea433b2e)
- Merge pull request #574 from Calcium-Ion/channel-tag [`48abfd05`](https://github.com/Veloera/Veloera/commit/48abfd05)
- Merge pull request #582 from prnake/patch-1 [`821f3a75`](https://github.com/Veloera/Veloera/commit/821f3a75)
- Update README.md [`b71e33b0`](https://github.com/Veloera/Veloera/commit/b71e33b0)
- Update README.md [`e5778837`](https://github.com/Veloera/Veloera/commit/e5778837)
- Update README.md [`78cac708`](https://github.com/Veloera/Veloera/commit/78cac708)
- Merge pull request #579 from HynoR/main [`76f74746`](https://github.com/Veloera/Veloera/commit/76f74746)
- Update BT.md [`0dd1953c`](https://github.com/Veloera/Veloera/commit/0dd1953c)
- Update BT.md [`019361a7`](https://github.com/Veloera/Veloera/commit/019361a7)
- Update README.md [`9b9d73e7`](https://github.com/Veloera/Veloera/commit/9b9d73e7)
- Update README.md [`05b5d6f2`](https://github.com/Veloera/Veloera/commit/05b5d6f2)
- Chore: support gpt-4o-2024-11-20 [`79b6c0a7`](https://github.com/Veloera/Veloera/commit/79b6c0a7)
- Merge pull request #493 from xixingya/feature/bug-fix [`462c2cc1`](https://github.com/Veloera/Veloera/commit/462c2cc1)
- Merge pull request #569 from utopeadia/main [`6a42ccf0`](https://github.com/Veloera/Veloera/commit/6a42ccf0)
- Merge branch 'main' into main [`7aa7114b`](https://github.com/Veloera/Veloera/commit/7aa7114b)
- Merge pull request #570 from leezhuuuuu/main [`c3e6b240`](https://github.com/Veloera/Veloera/commit/c3e6b240)
- 增加对于gemini-exp-1114模型的支持，映射到v1beta [`5d96f7b2`](https://github.com/Veloera/Veloera/commit/5d96f7b2)
- Modify the default gemini API to v1beta [`8eb32e9b`](https://github.com/Veloera/Veloera/commit/8eb32e9b)
- Merge pull request #564 from Licoy/main [`8baeece3`](https://github.com/Veloera/Veloera/commit/8baeece3)
- Merge pull request #563 from Licoy/main [`0b0bcbab`](https://github.com/Veloera/Veloera/commit/0b0bcbab)
- 修复独立日志数据库查询令牌日志时错误问题 [`450bea8f`](https://github.com/Veloera/Veloera/commit/450bea8f)
- 优化设置页面的模块间距与部分数据获取提示 [`bf75df8f`](https://github.com/Veloera/Veloera/commit/bf75df8f)
- 封装OAuth2授权回调页面 [`c6dae4b8`](https://github.com/Veloera/Veloera/commit/c6dae4b8)
- Merge pull request #505 from OiAnthony/f_dotenv [`a5abd40f`](https://github.com/Veloera/Veloera/commit/a5abd40f)
- Merge pull request #562 from seefs001/main [`6d47b2c5`](https://github.com/Veloera/Veloera/commit/6d47b2c5)
- Merge pull request #555 from utopeadia/main [`be652fa3`](https://github.com/Veloera/Veloera/commit/be652fa3)
- Continue fixing Ollama embedding return issue [`2ffa4268`](https://github.com/Veloera/Veloera/commit/2ffa4268)
- Merge pull request #552 from utopeadia/main [`3037dfab`](https://github.com/Veloera/Veloera/commit/3037dfab)
- Modify ollama embed return fields [`5253a0e7`](https://github.com/Veloera/Veloera/commit/5253a0e7)
- Update README.md [`e5588fc1`](https://github.com/Veloera/Veloera/commit/e5588fc1)
- Merge pull request #551 from Calcium-Ion/realtime [`a859ff59`](https://github.com/Veloera/Veloera/commit/a859ff59)
- Update dto [`e3c85572`](https://github.com/Veloera/Veloera/commit/e3c85572)
- Merge pull request #549 from HynoR/main [`3785e9d7`](https://github.com/Veloera/Veloera/commit/3785e9d7)
- Sync Latest Claude Model [`902a66b6`](https://github.com/Veloera/Veloera/commit/902a66b6)
- Merge pull request #548 from utopeadia/main [`aaf3f09e`](https://github.com/Veloera/Veloera/commit/aaf3f09e)
- /api/embeddings is deprecated, use /api/embed. [`e5235558`](https://github.com/Veloera/Veloera/commit/e5235558)
- Merge remote-tracking branch 'origin/main' [`8b8abfad`](https://github.com/Veloera/Veloera/commit/8b8abfad)
- Merge pull request #533 from HynoR/main [`62e321fe`](https://github.com/Veloera/Veloera/commit/62e321fe)
- Update model-ratio.go [`a2678a25`](https://github.com/Veloera/Veloera/commit/a2678a25)
- Update README.md [`334a6f82`](https://github.com/Veloera/Veloera/commit/334a6f82)
- Merge pull request #509 from Calcium-Ion/playground [`af02cdc5`](https://github.com/Veloera/Veloera/commit/af02cdc5)
- Merge branch 'Calcium-Ion:main' into main [`b0d655e2`](https://github.com/Veloera/Veloera/commit/b0d655e2)
- 撤回修改 [`e4ccaddf`](https://github.com/Veloera/Veloera/commit/e4ccaddf)
- 更新LoginForm 提示 [`b139588a`](https://github.com/Veloera/Veloera/commit/b139588a)
- Update README.md [`ee17e307`](https://github.com/Veloera/Veloera/commit/ee17e307)
- 使用postMessage向iframe传参theme-mode，实现切换子页面主题的功能 子页面的js示例 ``` <script> // 接收父页面的主题模式 window.addEventListener('message', function(event) { if (event.data.themeMode) { var theme = event.data.themeMode; // 测试是否正确接受到theme-mode的值 // console.log('Received theme mode from parent:', theme); applyTheme(theme); } }); [`574d7a09`](https://github.com/Veloera/Veloera/commit/574d7a09)
- 手动合并upstream [`edce1f70`](https://github.com/Veloera/Veloera/commit/edce1f70)
- ratio must gte 0 [`e0f19e5e`](https://github.com/Veloera/Veloera/commit/e0f19e5e)
- ratio must gte 0 [`3d33079d`](https://github.com/Veloera/Veloera/commit/3d33079d)
- ratio must gte 0 [`1d064a2e`](https://github.com/Veloera/Veloera/commit/1d064a2e)
- ratio must gte 0 [`4eae3b21`](https://github.com/Veloera/Veloera/commit/4eae3b21)
- 聊天按钮适配移动端 [`bab718e9`](https://github.com/Veloera/Veloera/commit/bab718e9)
- Merge branch 'Calcium-Ion:main' into main [`509a1a0b`](https://github.com/Veloera/Veloera/commit/509a1a0b)
- Merge branch 'Calcium-Ion:main' into main [`0b2ae561`](https://github.com/Veloera/Veloera/commit/0b2ae561)
- update HeaderBar [`016e071d`](https://github.com/Veloera/Veloera/commit/016e071d)
- Create FUNDING.yml [`5f379805`](https://github.com/Veloera/Veloera/commit/5f379805)
- Update logo [`e31022c6`](https://github.com/Veloera/Veloera/commit/e31022c6)
- Merge pull request #439 from guoruqiang/main [`fff7609f`](https://github.com/Veloera/Veloera/commit/fff7609f)
- Update README.md [`131453da`](https://github.com/Veloera/Veloera/commit/131453da)
- Merge branch 'main' into g-main [`ed948c12`](https://github.com/Veloera/Veloera/commit/ed948c12)
- Update README.md [`6263616c`](https://github.com/Veloera/Veloera/commit/6263616c)
- Merge branch 'Calcium-Ion:main' into main [`6bbf1d48`](https://github.com/Veloera/Veloera/commit/6bbf1d48)
- Merge pull request #464 from Yan-Zero/main [`1675679b`](https://github.com/Veloera/Veloera/commit/1675679b)
- add gemini exp [`0b5f2a70`](https://github.com/Veloera/Veloera/commit/0b5f2a70)
- Merge branch 'Calcium-Ion:main' into main [`b5bb7080`](https://github.com/Veloera/Veloera/commit/b5bb7080)
- Merge branch 'Calcium-Ion:main' into main [`a0d20896`](https://github.com/Veloera/Veloera/commit/a0d20896)
- Merge pull request #459 from HynoR/main [`5cab06d1`](https://github.com/Veloera/Veloera/commit/5cab06d1)
- Merge branch 'Calcium-Ion:main' into main [`a0673ef2`](https://github.com/Veloera/Veloera/commit/a0673ef2)
- Merge remote-tracking branch 'origin/main' [`416f831a`](https://github.com/Veloera/Veloera/commit/416f831a)
- Update Cohere Safety Setting [`0b4317ce`](https://github.com/Veloera/Veloera/commit/0b4317ce)
- Merge pull request #451 from Nana7mi1/main [`12e2481a`](https://github.com/Veloera/Veloera/commit/12e2481a)
- Merge pull request #455 from HynoR/feat/cohere-update [`27070906`](https://github.com/Veloera/Veloera/commit/27070906)
- Cohere Update [`722cc174`](https://github.com/Veloera/Veloera/commit/722cc174)
- Merge branch 'Calcium-Ion:main' into main [`2223aeb0`](https://github.com/Veloera/Veloera/commit/2223aeb0)
- Merge branch 'Calcium-Ion:main' into main [`ecf2f7f2`](https://github.com/Veloera/Veloera/commit/ecf2f7f2)
- Merge branch 'Calcium-Ion:main' into main [`033359e9`](https://github.com/Veloera/Veloera/commit/033359e9)
- Update go.mod [`c4182054`](https://github.com/Veloera/Veloera/commit/c4182054)
- Update README.md [`228f0c5e`](https://github.com/Veloera/Veloera/commit/228f0c5e)
- Merge pull request #448 from Calcium-Ion/vertex [`8a5e074f`](https://github.com/Veloera/Veloera/commit/8a5e074f)
- Merge pull request #2 from j471782517/main [`1379d7f1`](https://github.com/Veloera/Veloera/commit/1379d7f1)
- 增加环境变量GENERATE_DEFAULT_TOKEN 设置之后将生成初始令牌，默认关闭。 [`716bf6f4`](https://github.com/Veloera/Veloera/commit/716bf6f4)
- Merge branch 'Calcium-Ion:main' into main [`2422eb28`](https://github.com/Veloera/Veloera/commit/2422eb28)
- Merge branch 'main' of https://github.com/Calcium-Ion/new-api [`5b2082ba`](https://github.com/Veloera/Veloera/commit/5b2082ba)
- Merge pull request #445 from OswinWu/fix-outlook-ofb [`e3087e9b`](https://github.com/Veloera/Veloera/commit/e3087e9b)
- 增加注册自动生成初始令牌。 [`c97e2875`](https://github.com/Veloera/Veloera/commit/c97e2875)
- 修改提示时间。 [`64794630`](https://github.com/Veloera/Veloera/commit/64794630)
- update App.js [`fc5055c7`](https://github.com/Veloera/Veloera/commit/fc5055c7)
- 重新修改了chat [`27eb3584`](https://github.com/Veloera/Veloera/commit/27eb3584)
- Update Chat [`6810ee0a`](https://github.com/Veloera/Veloera/commit/6810ee0a)
- Merge pull request #427 from QuentinHsu/fix-log-pagination [`59d06a55`](https://github.com/Veloera/Veloera/commit/59d06a55)
- Merge pull request #426 from OswinWu/fix-log-page [`1b900e39`](https://github.com/Veloera/Veloera/commit/1b900e39)
- Merge pull request #432 from xixingya/feat-add-logdb [`accbae39`](https://github.com/Veloera/Veloera/commit/accbae39)
- support log db [`d82bd203`](https://github.com/Veloera/Veloera/commit/d82bd203)
- add log db [`0c01f49b`](https://github.com/Veloera/Veloera/commit/0c01f49b)
- Merge branch 'Calcium-Ion:main' into fix-log-page [`228104e8`](https://github.com/Veloera/Veloera/commit/228104e8)
- Merge pull request #425 from dalefengs/fix_group [`e4953548`](https://github.com/Veloera/Veloera/commit/e4953548)
- Merge pull request #415 from dalefengs/fix_group [`43076c2f`](https://github.com/Veloera/Veloera/commit/43076c2f)
- fix bug [`44902581`](https://github.com/Veloera/Veloera/commit/44902581)
- Merge pull request #409 from utopeadia/main [`8af0d9f2`](https://github.com/Veloera/Veloera/commit/8af0d9f2)
- 修改readme错误 [`afd328ef`](https://github.com/Veloera/Veloera/commit/afd328ef)
- Merge pull request #405 from utopeadia/main [`54657ec2`](https://github.com/Veloera/Veloera/commit/54657ec2)
- Merge pull request #399 from kakingone/main [`ae6b4e0b`](https://github.com/Veloera/Veloera/commit/ae6b4e0b)
- Update README.md [`fc0db450`](https://github.com/Veloera/Veloera/commit/fc0db450)
- 修改Gemini版本获取逻辑 [`22a98c58`](https://github.com/Veloera/Veloera/commit/22a98c58)
- Merge pull request #400 from OswinWu/feat-gitignore-web-dist [`7f2a2a7d`](https://github.com/Veloera/Veloera/commit/7f2a2a7d)
- Merge pull request #401 from HynoR/main [`cce7d025`](https://github.com/Veloera/Veloera/commit/cce7d025)
- Support cloudflare llama3.1-8b [`c5e8d7ec`](https://github.com/Veloera/Veloera/commit/c5e8d7ec)
- addupload [`2100d8ee`](https://github.com/Veloera/Veloera/commit/2100d8ee)
- Merge pull request #391 from OswinWu/fix-outlook-smtp [`d4a5df73`](https://github.com/Veloera/Veloera/commit/d4a5df73)
- [fix] fix send email error using outlook smtp [`da490db6`](https://github.com/Veloera/Veloera/commit/da490db6)
- Merge pull request #380 from crabkun/main [`b0d9756c`](https://github.com/Veloera/Veloera/commit/b0d9756c)
- Merge pull request #383 from Yan-Zero/main [`9dc07a85`](https://github.com/Veloera/Veloera/commit/9dc07a85)
- Update README.md [`733b3745`](https://github.com/Veloera/Veloera/commit/733b3745)
- Merge pull request #372 from Calcium-Ion/image [`a6a2d52f`](https://github.com/Veloera/Veloera/commit/a6a2d52f)
- Merge pull request #367 from Calcium-Ion/audio [`0f94ff47`](https://github.com/Veloera/Veloera/commit/0f94ff47)
- Merge pull request #371 from daggeryu/patch-1 [`9a8fd5cd`](https://github.com/Veloera/Veloera/commit/9a8fd5cd)
- Update README.md [`11856ab3`](https://github.com/Veloera/Veloera/commit/11856ab3)
- Merge pull request #363 from dalefengs/main [`5e936b39`](https://github.com/Veloera/Veloera/commit/5e936b39)
- Merge pull request #362 from dalefengs/main [`5be4cbca`](https://github.com/Veloera/Veloera/commit/5be4cbca)
- Update README.md [`3ed4f2f0`](https://github.com/Veloera/Veloera/commit/3ed4f2f0)
- Update README.md [`bec18ed8`](https://github.com/Veloera/Veloera/commit/bec18ed8)
- fix typo [`a9e1078b`](https://github.com/Veloera/Veloera/commit/a9e1078b)
- Merge remote-tracking branch 'origin/main' [`55c28b2f`](https://github.com/Veloera/Veloera/commit/55c28b2f)
- Merge pull request #335 from HynoR/fix/v1 [`a9b97852`](https://github.com/Veloera/Veloera/commit/a9b97852)
- fix testAllChannels nil pointer panic [`37a0930d`](https://github.com/Veloera/Veloera/commit/37a0930d)
- Update README.md [`c834289f`](https://github.com/Veloera/Veloera/commit/c834289f)
- Merge pull request #331 from mageia/master [`bc649dda`](https://github.com/Veloera/Veloera/commit/bc649dda)
- Delete fly.toml [`c838beba`](https://github.com/Veloera/Veloera/commit/c838beba)
- Merge pull request #307 from think007/main [`0df1df4f`](https://github.com/Veloera/Veloera/commit/0df1df4f)
- Merge pull request #333 from Calcium-Ion/suno [`f6fcb2fd`](https://github.com/Veloera/Veloera/commit/f6fcb2fd)
- Merge pull request #330 from Calcium-Ion/suno [`23bfc4f6`](https://github.com/Veloera/Veloera/commit/23bfc4f6)
- Merge pull request #324 from Mgrsc/main [`31f0cfb2`](https://github.com/Veloera/Veloera/commit/31f0cfb2)
- add aws claude-3-5-sonnet [`4d348c04`](https://github.com/Veloera/Veloera/commit/4d348c04)
- Merge pull request #310 from Calcium-Ion/feat/suno-api [`1e2c1ee9`](https://github.com/Veloera/Veloera/commit/1e2c1ee9)
- Merge pull request #304 from akl7777777/main [`1289be04`](https://github.com/Veloera/Veloera/commit/1289be04)
- fix panic [`b069056b`](https://github.com/Veloera/Veloera/commit/b069056b)
- revert: hide web embed [`b82582cb`](https://github.com/Veloera/Veloera/commit/b82582cb)
- Midjourney Proxy Plus无实例账号自动禁用该渠道 [`a47111a0`](https://github.com/Veloera/Veloera/commit/a47111a0)
- Merge remote-tracking branch 'origin/main' [`4dd5233f`](https://github.com/Veloera/Veloera/commit/4dd5233f)
- Merge pull request #292 from Calcium-Ion/cf-worker [`8a279772`](https://github.com/Veloera/Veloera/commit/8a279772)
- 修改版权信息 [`1291504f`](https://github.com/Veloera/Veloera/commit/1291504f)
- Merge remote-tracking branch 'origin/main' [`54f17d60`](https://github.com/Veloera/Veloera/commit/54f17d60)
- Update LICENSE [`fcb85066`](https://github.com/Veloera/Veloera/commit/fcb85066)
- update README.md [`1e005398`](https://github.com/Veloera/Veloera/commit/1e005398)
- Merge pull request #261 from iszcz/new512 [`24722a8e`](https://github.com/Veloera/Veloera/commit/24722a8e)
- Merge pull request #271 from p3psi-boo/main [`c86bff38`](https://github.com/Veloera/Veloera/commit/c86bff38)
- 合并上游、支持已有渠道获取模型 [`6aa1f2fc`](https://github.com/Veloera/Veloera/commit/6aa1f2fc)
- 添加同步上游模型列表按钮：添加提示以及支持已有渠道获取 [`e2663a5c`](https://github.com/Veloera/Veloera/commit/e2663a5c)
- Merge pull request #266 from Calcium-Ion/custom-channel [`f421699e`](https://github.com/Veloera/Veloera/commit/f421699e)
- Merge pull request #272 from hepeichun/main [`f0c884cb`](https://github.com/Veloera/Veloera/commit/f0c884cb)
- fix:删除显示模型倍率都乘两倍的问题 [`1ab93717`](https://github.com/Veloera/Veloera/commit/1ab93717)
- 添加同步上游模型列表按钮 [`6fe643b1`](https://github.com/Veloera/Veloera/commit/6fe643b1)
- Merge pull request #265 from jimmyshjj/original [`de81eba9`](https://github.com/Veloera/Veloera/commit/de81eba9)
- Merge branch 'new512' of https://github.com/iszcz/new-api into new512 [`1deb935f`](https://github.com/Veloera/Veloera/commit/1deb935f)
- 价格页修复 [`0caa639d`](https://github.com/Veloera/Veloera/commit/0caa639d)
- Update Perplexity and 01 models [`ea0c99ac`](https://github.com/Veloera/Veloera/commit/ea0c99ac)
- Add files via upload [`afc2289b`](https://github.com/Veloera/Veloera/commit/afc2289b)
- 优化价格页，支持大小写模糊搜素 [`472145ae`](https://github.com/Veloera/Veloera/commit/472145ae)
- Merge branch 'Calcium-Ion:main' into new512 [`f956e448`](https://github.com/Veloera/Veloera/commit/f956e448)
- Merge pull request #257 from p3psi-boo/main [`e3b885b7`](https://github.com/Veloera/Veloera/commit/e3b885b7)
- Merge pull request #259 from jimmyshjj/original [`55962acf`](https://github.com/Veloera/Veloera/commit/55962acf)
- Squashed commit of the following: [`d33b802d`](https://github.com/Veloera/Veloera/commit/d33b802d)
- Merge branch 'Calcium-Ion:main' into main [`63d68ce7`](https://github.com/Veloera/Veloera/commit/63d68ce7)
- 修复渠道测试没有走模型映射 [`95ac7c34`](https://github.com/Veloera/Veloera/commit/95ac7c34)
- 价格页样式修改 [`b1019be7`](https://github.com/Veloera/Veloera/commit/b1019be7)
- Merge remote-tracking branch 'origin/main' [`7b583052`](https://github.com/Veloera/Veloera/commit/7b583052)
- Merge pull request #252 from utopeadia/main [`8faf5d25`](https://github.com/Veloera/Veloera/commit/8faf5d25)
- Update constant.go [`a3a6733f`](https://github.com/Veloera/Veloera/commit/a3a6733f)
- Update model-ratio.go [`0f11461a`](https://github.com/Veloera/Veloera/commit/0f11461a)
- Update adaptor.go [`a5b84ba5`](https://github.com/Veloera/Veloera/commit/a5b84ba5)
- Merge pull request #251 from congyijiu/main [`c222bc87`](https://github.com/Veloera/Veloera/commit/c222bc87)
- Update constant.go [`3dd2a5bf`](https://github.com/Veloera/Veloera/commit/3dd2a5bf)
- Merge pull request #248 from QuentinHsu/refactor-settings-operation [`ced67b9b`](https://github.com/Veloera/Veloera/commit/ced67b9b)
- Merge pull request #247 from MapleEve/gpt4o [`9a9fd34c`](https://github.com/Veloera/Veloera/commit/9a9fd34c)
- Merge branch 'main' into refactor-settings-operation [`0ddb67f9`](https://github.com/Veloera/Veloera/commit/0ddb67f9)
- ✨ add: Support GPT4o ratio [`256ccfa9`](https://github.com/Veloera/Veloera/commit/256ccfa9)
- ✨ add: Support GPT4o [`6c059d5b`](https://github.com/Veloera/Veloera/commit/6c059d5b)
- Merge pull request #245 from Calcium-Ion/pricing-page [`acbc3649`](https://github.com/Veloera/Veloera/commit/acbc3649)
- Merge branch 'main' into refactor-settings-operation [`698af078`](https://github.com/Veloera/Veloera/commit/698af078)
- Merge pull request #242 from iszcz/new512 [`5ac3d25f`](https://github.com/Veloera/Veloera/commit/5ac3d25f)
- 渠道批量添加模型 [`12667ad1`](https://github.com/Veloera/Veloera/commit/12667ad1)
- Merge remote-tracking branch 'origin/main' [`1cff3c10`](https://github.com/Veloera/Veloera/commit/1cff3c10)
- Merge pull request #232 from kakingone/add-mj-usetime [`637801fb`](https://github.com/Veloera/Veloera/commit/637801fb)
- --amend [`675de89c`](https://github.com/Veloera/Veloera/commit/675de89c)
- fix typo [`2cb10b00`](https://github.com/Veloera/Veloera/commit/2cb10b00)
- Merge pull request #213 from iszcz/pr [`608ec287`](https://github.com/Veloera/Veloera/commit/608ec287)
- 用户管理页新增分组查询 [`79cf7068`](https://github.com/Veloera/Veloera/commit/79cf7068)
- update makefile [`6e54f014`](https://github.com/Veloera/Veloera/commit/6e54f014)
- update makefile [`505916b7`](https://github.com/Veloera/Veloera/commit/505916b7)
- Merge pull request #208 from kahosan/refactor_dark_mode [`9dfd405b`](https://github.com/Veloera/Veloera/commit/9dfd405b)
- Merge pull request #194 from iszcz/pr [`6c5b94ce`](https://github.com/Veloera/Veloera/commit/6c5b94ce)
- Merge pull request #205 from MapleEve/main [`ac298431`](https://github.com/Veloera/Veloera/commit/ac298431)
- add: new Gemini model default ratio [`1456992a`](https://github.com/Veloera/Veloera/commit/1456992a)
- Merge branch 'Calcium-Ion:main' into pr [`b31fadd7`](https://github.com/Veloera/Veloera/commit/b31fadd7)
- Merge pull request #197 from xqx333/main [`300947f4`](https://github.com/Veloera/Veloera/commit/300947f4)
- Update model-ratio.go [`bf94893f`](https://github.com/Veloera/Veloera/commit/bf94893f)
- Merge branch 'Calcium-Ion:main' into pr [`97af77b2`](https://github.com/Veloera/Veloera/commit/97af77b2)
- update model-ratio [`4ef2422b`](https://github.com/Veloera/Veloera/commit/4ef2422b)
- 新增渠道复制 [`08e10df8`](https://github.com/Veloera/Veloera/commit/08e10df8)
- Merge pull request #183 from iszcz/patch-1 [`0a49715c`](https://github.com/Veloera/Veloera/commit/0a49715c)
- Merge pull request #185 from h1xy/main [`89efed48`](https://github.com/Veloera/Veloera/commit/89efed48)
- Merge pull request #188 from Calcium-Ion/fix/many-model-error [`97e0aae0`](https://github.com/Veloera/Veloera/commit/97e0aae0)
- Fix: CompletionRatio is not working for openrouter.ai [`c5f6d0e0`](https://github.com/Veloera/Veloera/commit/c5f6d0e0)
- update go-epay [`2d1d1b46`](https://github.com/Veloera/Veloera/commit/2d1d1b46)
- 清除--mode [`5961de03`](https://github.com/Veloera/Veloera/commit/5961de03)
- update README.md [`fbdb1702`](https://github.com/Veloera/Veloera/commit/fbdb1702)
- update README.md [`497cc326`](https://github.com/Veloera/Veloera/commit/497cc326)
- fix bug [`c040fa22`](https://github.com/Veloera/Veloera/commit/c040fa22)
- update README.md [`cdf20879`](https://github.com/Veloera/Veloera/commit/cdf20879)
- Merge remote-tracking branch 'origin/main' [`3d0f77ff`](https://github.com/Veloera/Veloera/commit/3d0f77ff)
- Merge pull request #171 from QuentinHsu/perf-setting-tab-navigation [`5a5b7d61`](https://github.com/Veloera/Veloera/commit/5a5b7d61)
- Merge pull request #175 from ye4293/test [`ad8ce915`](https://github.com/Veloera/Veloera/commit/ad8ce915)
- Merge pull request #176 from QuentinHsu/perf-helpers-renderGroup [`456fb875`](https://github.com/Veloera/Veloera/commit/456fb875)
- Update misc.go [`224746b4`](https://github.com/Veloera/Veloera/commit/224746b4)
- Merge pull request #174 from AI-ASS/main [`ac827b18`](https://github.com/Veloera/Veloera/commit/ac827b18)
- Rename .prettierrc.mjs to .prettierrc.mjs [`658bf2ad`](https://github.com/Veloera/Veloera/commit/658bf2ad)
- Merge pull request #172 from MapleEve/main [`c25f48b7`](https://github.com/Veloera/Veloera/commit/c25f48b7)
- Merge pull request #167 from weikecloud/main [`87919b03`](https://github.com/Veloera/Veloera/commit/87919b03)
- Update midjourney.go [`f7a4f18a`](https://github.com/Veloera/Veloera/commit/f7a4f18a)
- 增加上游构图失败判断 [`706449de`](https://github.com/Veloera/Veloera/commit/706449de)
- Merge remote-tracking branch 'origin/main' [`d80a7d3c`](https://github.com/Veloera/Veloera/commit/d80a7d3c)
- Merge pull request #165 from xyfacai/fork/mj-mode-path [`2cca2a98`](https://github.com/Veloera/Veloera/commit/2cca2a98)
- Merge remote-tracking branch 'origin/main' [`72962e98`](https://github.com/Veloera/Veloera/commit/72962e98)
- Merge pull request #145 from QuentinHsu/fix-dev-error [`01e3acfa`](https://github.com/Veloera/Veloera/commit/01e3acfa)
- Merge remote-tracking branch 'origin/main' [`63304a5b`](https://github.com/Veloera/Veloera/commit/63304a5b)
- Merge pull request #141 from Calcium-Ion/vite-support [`0618f03c`](https://github.com/Veloera/Veloera/commit/0618f03c)
- update Dockerfile [`f823b4d4`](https://github.com/Veloera/Veloera/commit/f823b4d4)
- Merge pull request #137 from MapleEve/main [`a500097b`](https://github.com/Veloera/Veloera/commit/a500097b)
- Merge branch 'Calcium-Ion:main' into main [`a825699e`](https://github.com/Veloera/Veloera/commit/a825699e)
- Merge branch 'Calcium-Ion:main' into main [`492001a8`](https://github.com/Veloera/Veloera/commit/492001a8)
- Add: 01AI in readme [`7d64f30f`](https://github.com/Veloera/Veloera/commit/7d64f30f)
- fix empty url [`9e157ed8`](https://github.com/Veloera/Veloera/commit/9e157ed8)
- Add 01.AI relay [`cfabf8a6`](https://github.com/Veloera/Veloera/commit/cfabf8a6)
- Merge pull request #131 from Calcium-Ion/sensitive-words [`5e7774cf`](https://github.com/Veloera/Veloera/commit/5e7774cf)
- Update README.md [`bec21ade`](https://github.com/Veloera/Veloera/commit/bec21ade)
- Merge remote-tracking branch 'origin/main' [`615d109d`](https://github.com/Veloera/Veloera/commit/615d109d)
- Merge pull request #125 from wozulong/main [`0da49fa4`](https://github.com/Veloera/Veloera/commit/0da49fa4)
- Update README.md [`652bb4a5`](https://github.com/Veloera/Veloera/commit/652bb4a5)
- Update Midjourney.md [`a26b9a9b`](https://github.com/Veloera/Veloera/commit/a26b9a9b)
- Merge pull request #114 from Calcium-Ion/midjourney-proxy-plus [`f62dcbf6`](https://github.com/Veloera/Veloera/commit/f62dcbf6)
- Update README.md [`2786a6b5`](https://github.com/Veloera/Veloera/commit/2786a6b5)
- Merge remote-tracking branch 'origin/main' [`95d8059c`](https://github.com/Veloera/Veloera/commit/95d8059c)
- Merge pull request #113 from xyspg/main [`1e1a53e4`](https://github.com/Veloera/Veloera/commit/1e1a53e4)
- Update README.md [`9e5c6364`](https://github.com/Veloera/Veloera/commit/9e5c6364)
- Update README.md [`ac7407ce`](https://github.com/Veloera/Veloera/commit/ac7407ce)
- Merge pull request #98 from h1xy/main [`3ea061a8`](https://github.com/Veloera/Veloera/commit/3ea061a8)
- Update README.md [`cb50fcaf`](https://github.com/Veloera/Veloera/commit/cb50fcaf)
- Merge pull request #103 from Calcium-Ion/dev [`eca48268`](https://github.com/Veloera/Veloera/commit/eca48268)
- Merge pull request #96 from QuentinHsu/refactor-other-setting [`4186880e`](https://github.com/Veloera/Veloera/commit/4186880e)
- Update README.md [`9e0a54e9`](https://github.com/Veloera/Veloera/commit/9e0a54e9)
- Merge pull request #81 from QuentinHsu/fix-display-home-content [`da73dca9`](https://github.com/Veloera/Veloera/commit/da73dca9)
- Merge branch 'main' into fix-display-home-content [`415d2961`](https://github.com/Veloera/Veloera/commit/415d2961)
- Merge pull request #91 from QuentinHsu/fix-model-name-not-trimmed [`d5c5c303`](https://github.com/Veloera/Veloera/commit/d5c5c303)
- Merge pull request #79 from Ehco1996/telegram-login [`a5cfeeaa`](https://github.com/Veloera/Veloera/commit/a5cfeeaa)
- Merge branch 'main' into telegram-login [`02d5a5f1`](https://github.com/Veloera/Veloera/commit/02d5a5f1)
- address comment [`699fe256`](https://github.com/Veloera/Veloera/commit/699fe256)
- Merge branch 'fix-model-name' [`ca5c4d2d`](https://github.com/Veloera/Veloera/commit/ca5c4d2d)
- revert compose [`cb92d6fd`](https://github.com/Veloera/Veloera/commit/cb92d6fd)
- Merge pull request #1 from Ehco1996/telegram-login-complete [`e5cea801`](https://github.com/Veloera/Veloera/commit/e5cea801)
- Update makefile and user model, add Telegram integration [`194ff1ac`](https://github.com/Veloera/Veloera/commit/194ff1ac)
- Add Telegram OAuth support [`690d2e6b`](https://github.com/Veloera/Veloera/commit/690d2e6b)
- Add Telegram bot token and name [`140e843d`](https://github.com/Veloera/Veloera/commit/140e843d)
- Merge pull request #53 from Calcium-Ion/fork/main [`5b2377ee`](https://github.com/Veloera/Veloera/commit/5b2377ee)
- Merge remote-tracking branch 'origin/main' [`800f4946`](https://github.com/Veloera/Veloera/commit/800f4946)
- update package.json [`76016107`](https://github.com/Veloera/Veloera/commit/76016107)
- Merge pull request #51 from wxDadadada/main [`3a8be505`](https://github.com/Veloera/Veloera/commit/3a8be505)
- Update SiderBar.js [`51f7ad5d`](https://github.com/Veloera/Veloera/commit/51f7ad5d)
- Changes to be committed: modified: web/src/components/TokensTable.js [`f73a180f`](https://github.com/Veloera/Veloera/commit/f73a180f)
- 增加了一个超链聊天跳转 [`e8db0a2c`](https://github.com/Veloera/Veloera/commit/e8db0a2c)
- Merge remote-tracking branch 'origin/main' [`6757166d`](https://github.com/Veloera/Veloera/commit/6757166d)
- Merge pull request #45 from AI-ASS/patch-1 [`2ccd6c04`](https://github.com/Veloera/Veloera/commit/2ccd6c04)
- update README.md [`312417f3`](https://github.com/Veloera/Veloera/commit/312417f3)
- update README.md [`4f95b7d0`](https://github.com/Veloera/Veloera/commit/4f95b7d0)
- Update LICENSE [`954ed893`](https://github.com/Veloera/Veloera/commit/954ed893)
- update README.md [`f3f8cdc4`](https://github.com/Veloera/Veloera/commit/f3f8cdc4)
- 删除前端无用log [`33b93e1e`](https://github.com/Veloera/Veloera/commit/33b93e1e)
- Update README.md [`d68a5012`](https://github.com/Veloera/Veloera/commit/d68a5012)
- 修复mj计费问题 [`b3fc3359`](https://github.com/Veloera/Veloera/commit/b3fc3359)
- Merge pull request #41 from Calcium-Ion/optimize/mj [`290fed38`](https://github.com/Veloera/Veloera/commit/290fed38)
- Merge pull request #42 from Tailen/main [`aa6d6239`](https://github.com/Veloera/Veloera/commit/aa6d6239)
- optimize: MJ 部分调整、优化 [`5c747dfe`](https://github.com/Veloera/Veloera/commit/5c747dfe)
- update README.md [`89dd0e05`](https://github.com/Veloera/Veloera/commit/89dd0e05)
- 更新用户时刷新缓存数据 [`7e4fe148`](https://github.com/Veloera/Veloera/commit/7e4fe148)
- update README.md [`85411bc1`](https://github.com/Veloera/Veloera/commit/85411bc1)
- 无效的请求返回具体原因 [`b86bc169`](https://github.com/Veloera/Veloera/commit/b86bc169)
- Happy New Year [`1a8a2469`](https://github.com/Veloera/Veloera/commit/1a8a2469)
- enable parseTime=true [`c09f3b92`](https://github.com/Veloera/Veloera/commit/c09f3b92)
- 修复不能删除注销用户的问题 [`e8bcf60f`](https://github.com/Veloera/Veloera/commit/e8bcf60f)
- support gemini-pro-vision [`14592f97`](https://github.com/Veloera/Veloera/commit/14592f97)
- bump golang.org/x/crypto from 0.14.0 to 0.17.0 [`4036355f`](https://github.com/Veloera/Veloera/commit/4036355f)
- bump golang.org/x/crypto from 0.14.0 to 0.17.0 [`fa2efb73`](https://github.com/Veloera/Veloera/commit/fa2efb73)
- 修复用户管理显示问题 [`7a8344c4`](https://github.com/Veloera/Veloera/commit/7a8344c4)
- enable mysql parseTime [`6ad25444`](https://github.com/Veloera/Veloera/commit/6ad25444)
- Revert "fix delete user" [`7cd1261a`](https://github.com/Veloera/Veloera/commit/7cd1261a)
- fix delete user [`45ed973f`](https://github.com/Veloera/Veloera/commit/45ed973f)
- fix delete user [`07fe5a02`](https://github.com/Veloera/Veloera/commit/07fe5a02)
- update Dockerfile [`7a4969c2`](https://github.com/Veloera/Veloera/commit/7a4969c2)
- 用户注销后禁止再次注册 [`ad6842da`](https://github.com/Veloera/Veloera/commit/ad6842da)
- 恢复渠道优先级可设置为负数 [`3c52a099`](https://github.com/Veloera/Veloera/commit/3c52a099)
- update README.md [`7c4719b6`](https://github.com/Veloera/Veloera/commit/7c4719b6)
- fix VERSION [`b9d040cf`](https://github.com/Veloera/Veloera/commit/b9d040cf)
- fix gemini [`6e8ff8c0`](https://github.com/Veloera/Veloera/commit/6e8ff8c0)
- update README.md [`676dc957`](https://github.com/Veloera/Veloera/commit/676dc957)
- 优化gpt-4-gizmo-*日志 [`1c06bdda`](https://github.com/Veloera/Veloera/commit/1c06bdda)
- 支持设置模型按次计费 [`34756432`](https://github.com/Veloera/Veloera/commit/34756432)
- update GeneralOpenAIRequest [`45e1042e`](https://github.com/Veloera/Veloera/commit/45e1042e)
- log输出更多有效信息 [`f5a36a05`](https://github.com/Veloera/Veloera/commit/f5a36a05)
- 个人设置添加修改密码功能 [`5730c693`](https://github.com/Veloera/Veloera/commit/5730c693)
- fix gemini [`2d33283a`](https://github.com/Veloera/Veloera/commit/2d33283a)
- 添加gemini支持 [`e057c0e4`](https://github.com/Veloera/Veloera/commit/e057c0e4)
- 修复vision格式问题 [`b3f1da44`](https://github.com/Veloera/Veloera/commit/b3f1da44)
- update README.md [`f048cefe`](https://github.com/Veloera/Veloera/commit/f048cefe)
- 修改docker-compose.yml [`0226d94e`](https://github.com/Veloera/Veloera/commit/0226d94e)
- 修复无法指定渠道id的问题 [`42469cb7`](https://github.com/Veloera/Veloera/commit/42469cb7)
- 添加批量删除渠道功能 [`e1da1e31`](https://github.com/Veloera/Veloera/commit/e1da1e31)
- 美化绘画IU [`0fdd4fc6`](https://github.com/Veloera/Veloera/commit/0fdd4fc6)
- 修复 测试所有渠道按钮 失效的问题 [`261dc43c`](https://github.com/Veloera/Veloera/commit/261dc43c)
- Warning when SESSION_SECRET is 'random_string' [`6463e053`](https://github.com/Veloera/Veloera/commit/6463e053)
- Merge remote-tracking branch 'public/main' into latest [`c5f08a75`](https://github.com/Veloera/Veloera/commit/c5f08a75)
- update README.md [`8a9bd08d`](https://github.com/Veloera/Veloera/commit/8a9bd08d)
- Merge pull request #30 from YOMIkio/email [`751c33a6`](https://github.com/Veloera/Veloera/commit/751c33a6)
- 修复Safari无法拉起支付的问题 [`57f664d0`](https://github.com/Veloera/Veloera/commit/57f664d0)
- 修复gpts无法设置倍率的问题 [`a29e3765`](https://github.com/Veloera/Veloera/commit/a29e3765)
- update README.md [`766e2071`](https://github.com/Veloera/Veloera/commit/766e2071)
- 添加gpt-4-1106-vision-preview模型 [`4b93f185`](https://github.com/Veloera/Veloera/commit/4b93f185)
- 优化渠道余额显示 [`3b63d9bb`](https://github.com/Veloera/Veloera/commit/3b63d9bb)
- 令牌界面添加聊天按钮 [`b99b24f2`](https://github.com/Veloera/Veloera/commit/b99b24f2)
- 优化前端代码 [`ac11f4bc`](https://github.com/Veloera/Veloera/commit/ac11f4bc)
- support gpts [`44465a39`](https://github.com/Veloera/Veloera/commit/44465a39)
- update ISSUE_TEMPLATE [`4f0419c7`](https://github.com/Veloera/Veloera/commit/4f0419c7)
- 修复日志充值显示问题 [`07b4a8f5`](https://github.com/Veloera/Veloera/commit/07b4a8f5)
- 渠道余额改为保留两位小数 [`348e5a0d`](https://github.com/Veloera/Veloera/commit/348e5a0d)
- 充值改为保留两位小数 [`79d77586`](https://github.com/Veloera/Veloera/commit/79d77586)
- update docker-compose.yml [`5240971b`](https://github.com/Veloera/Veloera/commit/5240971b)
- 修复渠道管理无法点击分页的bug [`00af4756`](https://github.com/Veloera/Veloera/commit/00af4756)
- 适配渠道管理移动端 [`9cd3cd3c`](https://github.com/Veloera/Veloera/commit/9cd3cd3c)
- 修复渠道余额显示问题 [`b301be7f`](https://github.com/Veloera/Veloera/commit/b301be7f)
- fix pgsql [`aa07ada0`](https://github.com/Veloera/Veloera/commit/aa07ada0)
- fix gpt-4-1106-preview count image token [`1a7a10c3`](https://github.com/Veloera/Veloera/commit/1a7a10c3)
- update README.md [`821645e5`](https://github.com/Veloera/Veloera/commit/821645e5)
- Merge pull request #20 from Calcium-Ion/optimize/hign--cpu [`e095900d`](https://github.com/Veloera/Veloera/commit/e095900d)
- Merge remote-tracking branch 'public/main' into latest [`143e2792`](https://github.com/Veloera/Veloera/commit/143e2792)
- 消费日志添加用户调用前余额 [`3ee41362`](https://github.com/Veloera/Veloera/commit/3ee41362)
- Merge pull request #22 from Happy-clo/pr [`2ced102a`](https://github.com/Veloera/Veloera/commit/2ced102a)
- 修复兑换码复制bug [`0d469804`](https://github.com/Veloera/Veloera/commit/0d469804)
- 修复渠道管理没有测试按钮的bug [`65572264`](https://github.com/Veloera/Veloera/commit/65572264)
- 修复渠道多次加载的bug [`d6ce413c`](https://github.com/Veloera/Veloera/commit/d6ce413c)
- 修改版权信息 [`044a2dc7`](https://github.com/Veloera/Veloera/commit/044a2dc7)
- 更新渠道管理 [`7dc8b0ea`](https://github.com/Veloera/Veloera/commit/7dc8b0ea)
- 预扣费增加用户余额检测 [`6a2ebf75`](https://github.com/Veloera/Veloera/commit/6a2ebf75)
- 视觉优化 [`8b96d068`](https://github.com/Veloera/Veloera/commit/8b96d068)
- 修改错误提示 [`6e744bd1`](https://github.com/Veloera/Veloera/commit/6e744bd1)
- 优化编辑用户逻辑 [`cddcf9ca`](https://github.com/Veloera/Veloera/commit/cddcf9ca)
- fix mj fetch bug [`adfabaa1`](https://github.com/Veloera/Veloera/commit/adfabaa1)
- fix mj submit bug [`7ffc2e34`](https://github.com/Veloera/Veloera/commit/7ffc2e34)
- fix typo [`10ca1e61`](https://github.com/Veloera/Veloera/commit/10ca1e61)
- 优化一些交互逻辑 [`8551e07c`](https://github.com/Veloera/Veloera/commit/8551e07c)
- 优化一些交互逻辑 [`73adc312`](https://github.com/Veloera/Veloera/commit/73adc312)
- 更新修改默认密码的提示 [`23dde53c`](https://github.com/Veloera/Veloera/commit/23dde53c)
- 更新用户管理界面UI [`b140b326`](https://github.com/Veloera/Veloera/commit/b140b326)
- 个人设置可点击复制模型名称 [`4475d54c`](https://github.com/Veloera/Veloera/commit/4475d54c)
- 修复渠道页面出现多个分页组件的bug [`947b571a`](https://github.com/Veloera/Veloera/commit/947b571a)
- 修复tts异常时仍然扣费的问题 [`9ddafba7`](https://github.com/Veloera/Veloera/commit/9ddafba7)
- update status [`718749f4`](https://github.com/Veloera/Veloera/commit/718749f4)
- delete workflows [`3dab647e`](https://github.com/Veloera/Veloera/commit/3dab647e)
- update relay [`62ecee01`](https://github.com/Veloera/Veloera/commit/62ecee01)
- 移除检查更新 [`45275714`](https://github.com/Veloera/Veloera/commit/45275714)
- 修复流模式下客户端中断导致的计费异常问题 [`aa29868d`](https://github.com/Veloera/Veloera/commit/aa29868d)
- 移除更新提醒 [`a15f2d54`](https://github.com/Veloera/Veloera/commit/a15f2d54)
- 适配移动端 [`4f09b3c7`](https://github.com/Veloera/Veloera/commit/4f09b3c7)
- 修复令牌添加、搜索bug [`3121afd2`](https://github.com/Veloera/Veloera/commit/3121afd2)
- fix epay bug [`fe360c06`](https://github.com/Veloera/Veloera/commit/fe360c06)
- fix epay bug [`381c3543`](https://github.com/Veloera/Veloera/commit/381c3543)
- upgrade pkg [`a10f7cfb`](https://github.com/Veloera/Veloera/commit/a10f7cfb)
- Merge pull request #15 from Calcium-Ion/optimize/hign--cpu [`7a2d6885`](https://github.com/Veloera/Veloera/commit/7a2d6885)
- optimize: upgrade tiktoken pkg [`a5e973fb`](https://github.com/Veloera/Veloera/commit/a5e973fb)
- optimize: high cpu [`08c9f772`](https://github.com/Veloera/Veloera/commit/08c9f772)
- fix bug [`69f2c418`](https://github.com/Veloera/Veloera/commit/69f2c418)
- Merge pull request #14 from luxlzz6/main [`2ee3dbbc`](https://github.com/Veloera/Veloera/commit/2ee3dbbc)
- Merge pull request #13 from AI-ASS/main [`d4b3f873`](https://github.com/Veloera/Veloera/commit/d4b3f873)
- 绘图列表更新 [`3e89112b`](https://github.com/Veloera/Veloera/commit/3e89112b)
- fix the issue of model repetition [`15d3e981`](https://github.com/Veloera/Veloera/commit/15d3e981)
- Merge pull request #13 from Calcium-Ion/main [`734628e2`](https://github.com/Veloera/Veloera/commit/734628e2)
- add sqlite busy_timeout=5000 [`3808ae8d`](https://github.com/Veloera/Veloera/commit/3808ae8d)
- record relay time [`79492584`](https://github.com/Veloera/Veloera/commit/79492584)
- 删除relay-text中的consumeQuota变量 [`53ce2437`](https://github.com/Veloera/Veloera/commit/53ce2437)
- record all the consume log even if quota is 0 [`36b0db2a`](https://github.com/Veloera/Veloera/commit/36b0db2a)
- 修复可用模型显示已禁用模型 [`560753c6`](https://github.com/Veloera/Veloera/commit/560753c6)
- Merge pull request #8 from Calcium-Ion/main [`d5aca0ae`](https://github.com/Veloera/Veloera/commit/d5aca0ae)
- 添加pprof性能分析 [`9d191c10`](https://github.com/Veloera/Veloera/commit/9d191c10)
- 修复可用model重复显示bug [`f18f585f`](https://github.com/Veloera/Veloera/commit/f18f585f)
- Merge remote-tracking branch 'public/main' into latest [`ebd53029`](https://github.com/Veloera/Veloera/commit/ebd53029)
- rollback EditUser.js [`65694d26`](https://github.com/Veloera/Veloera/commit/65694d26)
- Update README.md [`79661397`](https://github.com/Veloera/Veloera/commit/79661397)
- 完善个人中心 [`fd57a1df`](https://github.com/Veloera/Veloera/commit/fd57a1df)
- fix email bug [`fa45f3ba`](https://github.com/Veloera/Veloera/commit/fa45f3ba)
- support base64 image [`57d0fc30`](https://github.com/Veloera/Veloera/commit/57d0fc30)
- Merge pull request #10 from luxlzz6/main [`6e670c0b`](https://github.com/Veloera/Veloera/commit/6e670c0b)
- Merge branch 'main' into main [`13e0cb64`](https://github.com/Veloera/Veloera/commit/13e0cb64)
- fix whisper-1 [`cf663e7a`](https://github.com/Veloera/Veloera/commit/cf663e7a)
- Update EditToken.js [`97f494d5`](https://github.com/Veloera/Veloera/commit/97f494d5)
- Update distributor.go [`f784f232`](https://github.com/Veloera/Veloera/commit/f784f232)
- Merge pull request #4 from Calcium-Ion/main [`98326bcd`](https://github.com/Veloera/Veloera/commit/98326bcd)
- Merge branch 'main' into latest [`81428751`](https://github.com/Veloera/Veloera/commit/81428751)
- update README.md [`3f085b61`](https://github.com/Veloera/Veloera/commit/3f085b61)
- fix image token calculate [`e5c2524f`](https://github.com/Veloera/Veloera/commit/e5c2524f)
- support gpt-4-1106-vision-preview [`7e0d2606`](https://github.com/Veloera/Veloera/commit/7e0d2606)
- fix image token calculate [`2d1ca2d9`](https://github.com/Veloera/Veloera/commit/2d1ca2d9)
- support gpt-4-1106-vision-preview [`a97bdebd`](https://github.com/Veloera/Veloera/commit/a97bdebd)
- Merge branch 'main' into latest [`fc494416`](https://github.com/Veloera/Veloera/commit/fc494416)
- 修复超时自动禁用bug [`51be7f28`](https://github.com/Veloera/Veloera/commit/51be7f28)
- Merge branch 'main' into latest [`0bb34040`](https://github.com/Veloera/Veloera/commit/0bb34040)
- 完善dall-e-3请求参数 [`9027ccf6`](https://github.com/Veloera/Veloera/commit/9027ccf6)
- 修复语音系列模型计费bug [`773e48ed`](https://github.com/Veloera/Veloera/commit/773e48ed)
- Merge branch 'main' into latest [`c75a92f6`](https://github.com/Veloera/Veloera/commit/c75a92f6)
- support tts [`63cd3f05`](https://github.com/Veloera/Veloera/commit/63cd3f05)
- try to fix email [`16ad764f`](https://github.com/Veloera/Veloera/commit/16ad764f)
- Update README.md [`15b5db66`](https://github.com/Veloera/Veloera/commit/15b5db66)
- Update README.md [`a7d7fb78`](https://github.com/Veloera/Veloera/commit/a7d7fb78)
- Merge pull request #5 from luxlzz6/main [`f3b1f276`](https://github.com/Veloera/Veloera/commit/f3b1f276)
- Update web/src/components/MjLogsTable.js [`6fbb6ca0`](https://github.com/Veloera/Veloera/commit/6fbb6ca0)
- Update token.go [`08b8b7b9`](https://github.com/Veloera/Veloera/commit/08b8b7b9)
- Update MjLogsTable.js [`30748216`](https://github.com/Veloera/Veloera/commit/30748216)
- Merge pull request #4 from luxlzz6/main [`8b2e4ec2`](https://github.com/Veloera/Veloera/commit/8b2e4ec2)
- Merge pull request #1 from Calcium-Ion/main [`a8c8dfc3`](https://github.com/Veloera/Veloera/commit/a8c8dfc3)
- Update relay-router.go [`d0e2cd9f`](https://github.com/Veloera/Veloera/commit/d0e2cd9f)
- Update relay.go [`cd1290d7`](https://github.com/Veloera/Veloera/commit/cd1290d7)
- 添加mj渠道 [`07d653ae`](https://github.com/Veloera/Veloera/commit/07d653ae)
- 修复聊天按钮bug [`5314272c`](https://github.com/Veloera/Veloera/commit/5314272c)
- add docker-image-amd64.yml [`b4bd9a19`](https://github.com/Veloera/Veloera/commit/b4bd9a19)
- 添加mj渠道 [`d09807af`](https://github.com/Veloera/Veloera/commit/d09807af)
- 修复聊天按钮bug [`2199cf23`](https://github.com/Veloera/Veloera/commit/2199cf23)
- Merge branch 'main' into latest [`6b4d30d7`](https://github.com/Veloera/Veloera/commit/6b4d30d7)
- delete workflow file [`d788c056`](https://github.com/Veloera/Veloera/commit/d788c056)
- Update README.md [`49136f29`](https://github.com/Veloera/Veloera/commit/49136f29)
- 修复余额不足邮件提醒bug [`3202ca9f`](https://github.com/Veloera/Veloera/commit/3202ca9f)
- Update README.md [`31d3fd1e`](https://github.com/Veloera/Veloera/commit/31d3fd1e)
- update the README.md [`41e7bc1e`](https://github.com/Veloera/Veloera/commit/41e7bc1e)
- 更新模型倍率 [`5b2d898b`](https://github.com/Veloera/Veloera/commit/5b2d898b)
- 更新模型倍率 [`6fbe1fe0`](https://github.com/Veloera/Veloera/commit/6fbe1fe0)
- 完全移除近似估算token功能 [`46930bc8`](https://github.com/Veloera/Veloera/commit/46930bc8)
- fix image bug [`7354606c`](https://github.com/Veloera/Veloera/commit/7354606c)
- fix log bug [`6ceaf9f6`](https://github.com/Veloera/Veloera/commit/6ceaf9f6)
- 修复令牌bug [`e3fca11f`](https://github.com/Veloera/Veloera/commit/e3fca11f)
- 修复兑换码bug [`f01932dc`](https://github.com/Veloera/Veloera/commit/f01932dc)
- fix name [`89be66b7`](https://github.com/Veloera/Veloera/commit/89be66b7)
- 完善兑换码界面 [`44d9eb9d`](https://github.com/Veloera/Veloera/commit/44d9eb9d)
- Merge branch 'main' into private [`f569ca27`](https://github.com/Veloera/Veloera/commit/f569ca27)
- 适配dall-e-3 [`3ff4210f`](https://github.com/Veloera/Veloera/commit/3ff4210f)
- fix name [`48733be5`](https://github.com/Veloera/Veloera/commit/48733be5)
- Revert "Edit index.html" [`7237d20e`](https://github.com/Veloera/Veloera/commit/7237d20e)
- Revert "fix name" [`3998dbfa`](https://github.com/Veloera/Veloera/commit/3998dbfa)
- Merge branch 'private' [`bd38c978`](https://github.com/Veloera/Veloera/commit/bd38c978)
- Create docker-image-private.yml [`cde0896e`](https://github.com/Veloera/Veloera/commit/cde0896e)
- fix name [`730966cc`](https://github.com/Veloera/Veloera/commit/730966cc)
- Edit index.html [`47b86212`](https://github.com/Veloera/Veloera/commit/47b86212)
- 日志添加查看信息功能 [`ec0d712b`](https://github.com/Veloera/Veloera/commit/ec0d712b)
- 钱包页面完善 [`4c738441`](https://github.com/Veloera/Veloera/commit/4c738441)
- 登录页完善 [`83050646`](https://github.com/Veloera/Veloera/commit/83050646)
- 登录支持人机验证 [`a0b975fc`](https://github.com/Veloera/Veloera/commit/a0b975fc)
- change name to miaoko api [`af8827d2`](https://github.com/Veloera/Veloera/commit/af8827d2)
- fix mj bug [`de596ce9`](https://github.com/Veloera/Veloera/commit/de596ce9)
- 令牌分页 [`3d87f868`](https://github.com/Veloera/Veloera/commit/3d87f868)
- 修复令牌bug [`e06186fe`](https://github.com/Veloera/Veloera/commit/e06186fe)
- 完善令牌界面 [`9eb8ad67`](https://github.com/Veloera/Veloera/commit/9eb8ad67)
- 完善令牌界面 [`0a945c16`](https://github.com/Veloera/Veloera/commit/0a945c16)
- 界面优化 [`f98dd8d6`](https://github.com/Veloera/Veloera/commit/f98dd8d6)
- Merge remote-tracking branch 'origin/main' [`719b82ad`](https://github.com/Veloera/Veloera/commit/719b82ad)
- 修复移动端显示bug [`db358fac`](https://github.com/Veloera/Veloera/commit/db358fac)
- Update docker-image.yml [`3906bf50`](https://github.com/Veloera/Veloera/commit/3906bf50)
- Merge remote-tracking branch 'origin/main' [`79bf6212`](https://github.com/Veloera/Veloera/commit/79bf6212)
- 修改logo [`11435364`](https://github.com/Veloera/Veloera/commit/11435364)
- Create docker-image.yml [`8d28c215`](https://github.com/Veloera/Veloera/commit/8d28c215)
- 更换前端组件库 [`e3b7f8bf`](https://github.com/Veloera/Veloera/commit/e3b7f8bf)
- Merge branch 'songquanpeng:main' into main [`7688e9f9`](https://github.com/Veloera/Veloera/commit/7688e9f9)
- Merge remote-tracking branch 'origin/main' [`fc2ef523`](https://github.com/Veloera/Veloera/commit/fc2ef523)
- fix bug [`921f1d65`](https://github.com/Veloera/Veloera/commit/921f1d65)
- Update the README.md [`eaa3e441`](https://github.com/Veloera/Veloera/commit/eaa3e441)
- 取消429自动禁用 [`2a3373cc`](https://github.com/Veloera/Veloera/commit/2a3373cc)
- Merge remote-tracking branch 'origin/main' [`3d55548c`](https://github.com/Veloera/Veloera/commit/3d55548c)
- Merge remote-tracking branch 'origin/main' [`3811575d`](https://github.com/Veloera/Veloera/commit/3811575d)
- 修改upscale倍率为五分之一 [`72d23621`](https://github.com/Veloera/Veloera/commit/72d23621)
- Merge branch 'songquanpeng:main' into main [`f82fde9d`](https://github.com/Veloera/Veloera/commit/f82fde9d)
- fix bug [`e6bb113f`](https://github.com/Veloera/Veloera/commit/e6bb113f)
- Merge branch 'songquanpeng:main' into main [`26f9d258`](https://github.com/Veloera/Veloera/commit/26f9d258)
- Merge branch 'main' of https://github.com/songquanpeng/one-api [`92001986`](https://github.com/Veloera/Veloera/commit/92001986)
- Merge branch 'songquanpeng:main' into main [`858c811b`](https://github.com/Veloera/Veloera/commit/858c811b)
- 合并最新代码 [`352ef053`](https://github.com/Veloera/Veloera/commit/352ef053)
- Merge remote-tracking branch 'origin/main' [`985e26fd`](https://github.com/Veloera/Veloera/commit/985e26fd)
- Merge remote-tracking branch 'origin/main' [`377da2df`](https://github.com/Veloera/Veloera/commit/377da2df)
- update: update the README.md [`9baa615a`](https://github.com/Veloera/Veloera/commit/9baa615a)
- 修复充值bug [`16aabbc4`](https://github.com/Veloera/Veloera/commit/16aabbc4)
- 修复充值bug [`01a66ff3`](https://github.com/Veloera/Veloera/commit/01a66ff3)
- fix bug [`0115d1d1`](https://github.com/Veloera/Veloera/commit/0115d1d1)
- 合并请求 [`443e2bd1`](https://github.com/Veloera/Veloera/commit/443e2bd1)
- Merge remote-tracking branch 'origin/main' [`9c08d783`](https://github.com/Veloera/Veloera/commit/9c08d783)
- 移除不必要的功能 [`9ab4f7a2`](https://github.com/Veloera/Veloera/commit/9ab4f7a2)
- merge [`43be1982`](https://github.com/Veloera/Veloera/commit/43be1982)
- 修复多路复用bug [`fe946562`](https://github.com/Veloera/Veloera/commit/fe946562)
- Merge branch 'main' of https://github.com/songquanpeng/one-api [`1c4409ae`](https://github.com/Veloera/Veloera/commit/1c4409ae)
- merge [`d9d5b001`](https://github.com/Veloera/Veloera/commit/d9d5b001)
- Merge remote-tracking branch 'origin/main' [`c0f0201f`](https://github.com/Veloera/Veloera/commit/c0f0201f)
- 修改stat接口返回值 [`3b0f1ae9`](https://github.com/Veloera/Veloera/commit/3b0f1ae9)
- add epay [`8f2119e4`](https://github.com/Veloera/Veloera/commit/8f2119e4)
- Revert "fix: add lock when update quota (close #399)" [`cac61b9f`](https://github.com/Veloera/Veloera/commit/cac61b9f)
- Merge branch 'main' of github.com:songquanpeng/one-api [`cc36bf9c`](https://github.com/Veloera/Veloera/commit/cc36bf9c)
- revert: do not enable turnstile check on login [`f61d3267`](https://github.com/Veloera/Veloera/commit/f61d3267)
- Merge branch 'main' of github.com:songquanpeng/one-api [`74b06b64`](https://github.com/Veloera/Veloera/commit/74b06b64)
- Temporary set rate limit to 60k [`c0bb2338`](https://github.com/Veloera/Veloera/commit/c0bb2338)
- Add docker pull badge [`963a5252`](https://github.com/Veloera/Veloera/commit/963a5252)
- Support custom channel now [`b3be4d8f`](https://github.com/Veloera/Veloera/commit/b3be4d8f)
- Redirect user if not logged in [`57379d8e`](https://github.com/Veloera/Veloera/commit/57379d8e)
- Update README [`d4c81295`](https://github.com/Veloera/Veloera/commit/d4c81295)
- Index page is done [`09be65e0`](https://github.com/Veloera/Veloera/commit/09be65e0)
- Update README [`78b4acdd`](https://github.com/Veloera/Veloera/commit/78b4acdd)
- Fix GitHub Actions [`396fa244`](https://github.com/Veloera/Veloera/commit/396fa244)
- Update README [`b6f602f5`](https://github.com/Veloera/Veloera/commit/b6f602f5)
- Update README [`81946c43`](https://github.com/Veloera/Veloera/commit/81946c43)
- Fix "HTTP decompression failed" [`b86e1dec`](https://github.com/Veloera/Veloera/commit/b86e1dec)
- Bug fix [`279ae8ad`](https://github.com/Veloera/Veloera/commit/279ae8ad)
- Relay done but not working [`852af57c`](https://github.com/Veloera/Veloera/commit/852af57c)
- Able to manage channels now [`9fc375c6`](https://github.com/Veloera/Veloera/commit/9fc375c6)
- Able to manage token now [`63da6dc6`](https://github.com/Veloera/Veloera/commit/63da6dc6)
- Token API done without verification [`b9082294`](https://github.com/Veloera/Veloera/commit/b9082294)
- Update README [`c30b069f`](https://github.com/Veloera/Veloera/commit/c30b069f)
- Update README [`f95f5948`](https://github.com/Veloera/Veloera/commit/f95f5948)
- Update system log [`5e8c22fb`](https://github.com/Veloera/Veloera/commit/5e8c22fb)
- Channel API done without verification [`61648292`](https://github.com/Veloera/Veloera/commit/61648292)
- Remove useless page [`af960070`](https://github.com/Veloera/Veloera/commit/af960070)
- Rename to One API [`539eac21`](https://github.com/Veloera/Veloera/commit/539eac21)
- Initial commit [`ab1f8a2b`](https://github.com/Veloera/Veloera/commit/ab1f8a2b)

