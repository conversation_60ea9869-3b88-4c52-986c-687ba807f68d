// Copyright (c) 2025 Tethys Plex
//
// This file is part of Veloera.
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.
package baidu_v2

var ModelList = []string{
	"ernie-4.0-8k-latest",
	"ernie-4.0-8k-preview",
	"ernie-4.0-8k",
	"ernie-4.0-turbo-8k-latest",
	"ernie-4.0-turbo-8k-preview",
	"ernie-4.0-turbo-8k",
	"ernie-4.0-turbo-128k",
	"ernie-3.5-8k-preview",
	"ernie-3.5-8k",
	"ernie-3.5-128k",
	"ernie-speed-8k",
	"ernie-speed-128k",
	"ernie-speed-pro-128k",
	"ernie-lite-8k",
	"ernie-lite-pro-128k",
	"ernie-tiny-8k",
	"ernie-char-8k",
	"ernie-char-fiction-8k",
	"ernie-novel-8k",
	"deepseek-v3",
	"deepseek-r1",
	"deepseek-r1-distill-qwen-32b",
	"deepseek-r1-distill-qwen-14b",
}

var ChannelName = "volcengine"
