/*
Copyright (c) 2025 Tethys Plex

This file is part of Veloera.

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program. If not, see <https://www.gnu.org/licenses/>.
*/
body {
  margin: 0;
  padding-top: 0;
  font-family:
    Lato, 'Helvetica Neue', Arial, Helvetica, 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scrollbar-width: none;
  color: var(--semi-color-text-0) !important;
  background-color: var(--semi-color-bg-0) !important;
  height: 100vh;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

#root
  > section
  > header
  > section
  > div
  > div
  > div
  > div.semi-navigation-header-list-outer
  > div.semi-navigation-list-wrapper
  > ul
  > div
  > a
  > li
  > span {
  font-weight: 600 !important;
}

.semi-descriptions-double-small .semi-descriptions-item {
  padding-right: 30px;
}

.semi-navigation-item {
  display: flex;
  align-items: center; /* 垂直居中 */
}

.semi-navigation-list a {
  display: block;      /* 确保 a 是块级元素，margin-bottom 才生效 */
  margin-bottom: 6px;
  border-radius: 99px;
}

.semi-navigation-list a li {
  border-radius: 99px;
}

.semi-navigation-list span {
  display: block;
  margin-bottom: 1px;
}

.semi-table-row-cell > div:first-of-type > * {
  margin-right: 3px !important;
}

.semi-banner {
  border-radius: 5px;
}

.semi-table-body {
  border-radius: 5px;
}

.semi-switch-checked {
  background-color: var(--semi-color-primary);
}

.semi-switch-checked:hover {
  background-color: var(--semi-color-primary-hover);
}

@media only screen and (max-width: 767px) {
  /*.semi-navigation-sub-wrap .semi-navigation-sub-title, .semi-navigation-item {*/
  /*  padding: 0 0;*/
  /*}*/
  .topnav {
    padding: 0 8px;
  }

  .topnav .semi-navigation-item {
    margin: 0 1px;
    padding: 0 4px;
  }

  .topnav .semi-navigation-list-wrapper {
    max-width: calc(55vw - 20px);
    overflow-x: auto;
    scrollbar-width: none;
  }
  #root
    > section
    > header
    > section
    > div
    > div
    > div
    > div.semi-navigation-footer
    > div
    > a
    > li {
    padding: 0 0;
  }
  #root
    > section
    > header
    > section
    > div
    > div
    > div
    > div.semi-navigation-header-list-outer
    > div.semi-navigation-list-wrapper
    > ul
    > div
    > a
    > li {
    padding: 0 5px;
  }
  #root
    > section
    > header
    > section
    > div
    > div
    > div
    > div.semi-navigation-footer
    > div:nth-child(1)
    > a
    > li {
    padding: 0 5px;
  }
  .semi-navigation-footer {
    padding-left: 0;
    padding-right: 0;
  }
  .semi-table-tbody,
  .semi-table-row,
  .semi-table-row-cell {
    display: block !important;
    width: auto !important;
    padding: 2px !important;
  }
  .semi-table-row-cell {
    border-bottom: 0 !important;
  }
  .semi-table-tbody > .semi-table-row {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .semi-space {
    /*display: block!important;*/
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    row-gap: 3px;
    column-gap: 10px;
  }

  .semi-navigation-horizontal .semi-navigation-header {
    margin-right: 0;
  }

  /* 确保移动端内容可滚动 */
  .semi-layout-content {
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior-y: auto !important;
  }

  /* 修复移动端下拉刷新 */
  body {
    overflow: visible !important;
    overscroll-behavior-y: auto !important;
    position: static !important;
    height: 100% !important;
  }

  /* 确保内容区域在移动端可以正常滚动 */
  #root {
    overflow: visible !important;
    height: 100% !important;
  }

  /* 隐藏在移动设备上 */
  .hide-on-mobile {
    display: none !important;
  }
}

.semi-table-tbody > .semi-table-row > .semi-table-row-cell {
  padding: 16px 14px;
}

.channel-table {
  .semi-table-tbody > .semi-table-row > .semi-table-row-cell {
    padding: 16px 8px;
  }
}

/*.semi-layout {*/
/*  height: 100%;*/
/*}*/

.tableShow {
  display: revert;
}

.semi-chat {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  height: 100%;
}

.semi-chat-chatBox-content {
  min-width: auto;
  word-break: break-word;
}

.tableHiddle {
  display: none !important;
}

body::-webkit-scrollbar {
  display: none;
}

code {
  font-family:
    source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}


/*.semi-navigation-vertical {*/
/*  !*flex: 0 0 auto;*!*/
/*  !*display: flex;*!*/
/*  !*flex-direction: column;*!*/
/*  !*width: 100%;*!*/
/*  height: 100%;*/
/*  overflow: hidden;*/
/*}*/

.main-content {
  padding: 4px;
  height: 100%;
}

.small-icon .icon {
  font-size: 1em !important;
}

.custom-footer {
  font-size: 1.1em;
}

/* 顶部栏样式 */
.topnav {
  padding: 0 16px;
}

.semi-banner {
  margin-bottom: 0.7rem;
}

.topnav .semi-navigation-item {
  border-radius: 4px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.topnav .semi-navigation-item:hover {
  background-color: var(--semi-color-primary-light-default);
  box-shadow: 0 2px 8px rgba(var(--semi-color-primary-rgb), 0.2);
}

.topnav .semi-navigation-item-selected {
  background-color: var(--semi-color-primary-light-default);
  color: var(--semi-color-primary);
  font-weight: 600;
}

/* 顶部栏文本样式 */
.header-bar-text {
  color: var(--semi-color-text-0);
  font-weight: 500;
  transition: all 0.3s ease;
}

.header-bar-text:hover {
  color: var(--semi-color-primary);
}

/* 自定义滚动条样式 */
.semi-layout-content::-webkit-scrollbar,
.semi-sider::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.semi-layout-content::-webkit-scrollbar-thumb,
.semi-sider::-webkit-scrollbar-thumb {
  background: var(--semi-color-tertiary-light-default);
  border-radius: 3px;
}

.semi-layout-content::-webkit-scrollbar-thumb:hover,
.semi-sider::-webkit-scrollbar-thumb:hover {
  background: var(--semi-color-tertiary);
}

.semi-layout-content::-webkit-scrollbar-track,
.semi-sider::-webkit-scrollbar-track {
  background: transparent;
}

/* Custom sidebar shadow */
/*.custom-sidebar-nav {*/
/*  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08) !important;*/
/*  -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08) !important;*/
/*  -moz-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08) !important;*/
/*  min-height: 100%;*/
/*}*/
