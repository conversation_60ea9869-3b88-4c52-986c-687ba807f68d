$font-form_label-fontWeight: $font-weight-regular;
$color-form_label_extra-text-default: var(--semi-color-text-2);
$spacing-form_label-marginBottom: 8px;
$color-tabs_tab_card-bg-focus: transparent;
$color-tabs_tab_card-bg-hover: transparent;
$color-tabs_tab_card-bg-active: transparent;
$color-tabs_tab_button-bg-hover: transparent;
$color-tabs_tab_button-bg-active: transparent;
$color-tabs_tab_button-text-default: var(--semi-color-text-0);
$color-tabs_tab_line_default-text-hover: var(--semi-color-primary-hover);
$color-tabs_tab_line_default-border-hover: transparent;
$color-tabs_tab_line_default-icon-default: var(--semi-color-text-0);
$color-tabs_tab_line_default-text-default: var(--semi-color-text-0);
$color-tabs_tab_line_selected-text-default: var(--semi-color-primary);
$color-tabs_tab_button_default-icon-default: var(--semi-color-text-0);
$color-tabs_tab_button_default-text-default: var(--semi-color-text-0);
$color-tabs_tab_card_default-border-default: var(--semi-color-border-2);
$color-tabs_tab_line_default-border-default: var(--semi-color-border-2);
$color-input_danger-bg-focus: var(--semi-color-bg-0);
$color-input_danger-bg-hover: var(--semi-color-bg-0);
$color-input_default-bg-focus: var(--semi-color-bg-0);
$color-input_default-bg-hover: var(--semi-color-bg-0);
$color-input_warning-bg-focus: var(--semi-color-bg-0);
$color-input_warning-bg-hover: var(--semi-color-bg-0);
$color-input_danger-bg-default: var(--semi-color-bg-0);
$color-input_default-bg-active: var(--semi-color-bg-0);
$color-input_default-bg-default: var(--semi-color-bg-0);
$color-input_warning-bg-default: var(--semi-color-bg-0);
$color-input_danger-border-hover: var(--semi-color-danger);
$color-input_default-border-hover: var(--semi-color-focus-border);
$color-input_warning-border-hover: var(--semi-color-warning);
$color-input_danger-border-default: var(--semi-color-danger);
$color-input_default-border-default: var(--semi-color-border-2);
$color-input_warning-border-default: var(--semi-color-warning);
$spacing-modal_content_withicon-marginLeft: 0px;
$font-radio_buttonRadio-hover-fontWeight: $font-weight-regular;
$font-radio_buttonRadio-default-fontWeight: $font-weight-regular;
$color-radio_default-bg-hover: var(--semi-color-bg-0);
$color-radio_checked-bg-disabled: var(--semi-color-disabled-bg);
$color-radio_buttonRadio-bg-default: var(--semi-color-bg-0);
$color-radio_default-border-default: var(--semi-color-border-3);
$color-radio_disabled-border-default: var(--semi-color-border-3);
$color-table-border-default: var(--semi-color-border-2);
$color-table_th-border-default: var(--semi-color-border-2);
$width-table_header_border: 1px;
$spacing-table_th-paddingTop: 12px;
$spacing-table_th-paddingBottom: 12px;
$spacing-toast_content-paddingTop: 12px;
$spacing-toast_content-paddingLeft: 16px;
$spacing-toast_content-paddingRight: 16px;
$color-button_primary-border-default: var(--semi-color-info-light-active);
$width-button-border: 2px;
$width-button-outline: 1px;
$height-button_default: 40px;
$font-select_keyword-fontWeight: 500;
$color-select-bg-hover: var(--semi-color-bg-0);
$color-select-bg-active: var(--semi-color-bg-0);
$color-select-bg-default: var(--semi-color-bg-0);
$color-select-border-hover: var(--semi-color-primary);
$color-select-border-default: var(--semi-color-border-2);
$color-select_option-icon-active: var(--semi-color-primary);
$spacing-select_option-paddingLeft: 12px;
$spacing-select_option-paddingRight: 12px;
$color-switch_checked-bg-foucs: var(--semi-color-primary-active);
$color-switch_checked-bg-hover: var(--semi-color-primary-hover);
$color-switch_checked-bg-default: var(--semi-color-primary);
$color-switch_checked_disabled-bg-default: var(--semi-color-primary-disabled);
$color-cascader_option-bg-hover: var(--semi-color-fill-1);
$color-cascader_option-bg-selected: var(--semi-color-fill-1);
$spacing-cacader_option_list-paddingX: 4px;
$spacing-cascader_selection_tag-marginX: 4px;
$spacing-cascader_selection_tag-marginY: 4px;
$spacing-collapse_header-marginX: 0;
$spacing-collapse_header-marginY: 0;
$spacing-collapse_header-padding: 0;
$spacing-dropdown_menu-paddingX: 4px;
$color-progress_track_inner-bg: var(--semi-color-primary);
$height-navigation_item_base: 48px;
$spacing-navigation_item-paddingY: 14px;
$color-pagination_item-bg-active: var(--semi-color-primary-light-default);
