name: Generate Changelog

on:
  workflow_dispatch:

jobs:
  changelog:
    runs-on: ubuntu-latest
    name: Generate and Commit Changelog
    permissions:
      contents: write

    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4

    - name: Checkout main branch and pull latest
      run: |
        git checkout main
        git pull origin main

    - name: Generate Changelog
      uses: smichard/conventional_changelog@2.0.0
      with:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Set Git User Info
      run: |
        git config user.name 'Veloera Bot'
        git config user.email '<EMAIL>'

    - name: Commit Changelog
      run: |
        git add CHANGELOG.md
        git commit -m "docs: :robot: changelog file generated" || echo "No changes to commit"
        git push origin main
