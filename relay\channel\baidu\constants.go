// Copyright (c) 2025 Tethys Plex
//
// This file is part of Veloera.
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.
package baidu

var ModelList = []string{
	"ERNIE-4.0-8K",
	"ERNIE-3.5-8K",
	"ERNIE-3.5-8K-0205",
	"ERNIE-3.5-8K-1222",
	"ERNIE-Bot-8K",
	"ERNIE-3.5-4K-0205",
	"ERNIE-Speed-8K",
	"ERNIE-Speed-128K",
	"ERNIE-Lite-8K-0922",
	"ERNIE-Lite-8K-0308",
	"ERNIE-Tiny-8K",
	"BLOOMZ-7B",
	"Embedding-V1",
	"bge-large-zh",
	"bge-large-en",
	"tao-8k",
}

var ChannelName = "baidu"
